# AWS Secrets Manager resources for development environment

# Stripe secrets
resource "aws_secretsmanager_secret" "stripe_publishable_key" {
  name        = "dev/stripe/publishable-key"
  description = "Stripe publishable key for development environment"
  
  tags = {
    Environment = "development"
    Service     = "stripe"
  }
}

resource "aws_secretsmanager_secret" "stripe_secret_key" {
  name        = "dev/stripe/secret-key"
  description = "Stripe secret key for development environment"
  
  tags = {
    Environment = "development"
    Service     = "stripe"
  }
}

resource "aws_secretsmanager_secret" "stripe_endpoint_secret" {
  name        = "dev/stripe/endpoint-secret"
  description = "Stripe webhook endpoint secret for development environment"
  
  tags = {
    Environment = "development"
    Service     = "stripe"
  }
}

# Resend API key
resource "aws_secretsmanager_secret" "resend_api_key" {
  name        = "dev/resend/api-key"
  description = "Resend API key for development environment"
  
  tags = {
    Environment = "development"
    Service     = "resend"
  }
}

# Frontend API routes (will be populated by infrastructure deployment)
resource "aws_secretsmanager_secret" "frontend_api_routes" {
  name        = "dev/frontend/api-routes"
  description = "API Gateway routes for frontend development environment"
  
  tags = {
    Environment = "development"
    Service     = "frontend"
  }
}

# Optional: Create secret versions with placeholder values
# These will be updated by the infrastructure deployment or manual setup
resource "aws_secretsmanager_secret_version" "frontend_api_routes_version" {
  secret_id = aws_secretsmanager_secret.frontend_api_routes.id
  secret_string = jsonencode({
    cart_api_route     = "https://9ubhtxv696.execute-api.us-west-2.amazonaws.com/test/dev-checkout-products"
    product_api_route  = "https://9ubhtxv696.execute-api.us-west-2.amazonaws.com/test/dev-list-products"
    contact_api_route  = "https://9ubhtxv696.execute-api.us-west-2.amazonaws.com/test/dev-contact-email"
    webhooks_api_route = "https://9ubhtxv696.execute-api.us-west-2.amazonaws.com/test/dev-webhooks"
  })
  
  lifecycle {
    ignore_changes = [secret_string]
  }
}
