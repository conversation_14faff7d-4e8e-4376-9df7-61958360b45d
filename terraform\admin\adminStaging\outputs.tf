# //./admin/adminStaging/outputs.tf
# output "get_lambda_arn" {
#   value = module.admin_lambda.get_lambda_arn
# }

# output "put_lambda_arn" {
#   value = module.admin_lambda.put_lambda_arn
# }

# output "post_lambda_arn" {
#   value = module.admin_lambda.post_lambda_arn
# }

# output "delete_lambda_arn" {
#   value = module.admin_lambda.delete_lambda_arn
# }

output "cognito_user_pool_client_id" {
  value = module.admin_cognito.admin_user_pool_client_id
  description = "The Client ID of the Cognito User Pool Client used in staging"
}

output "cognito_user_pool_id" {
  value = module.admin_cognito.admin_user_pool_id
  description = "The ID of the Cognito User Pool used in staging"
}


output "api_gateway_invoke_url" {
  value       = module.apigateway.api_gateway_invoke_url
  description = "Invoke URL for the Staging API Gateway"
}

output "admin_api_gateway_id" {
  value = module.apigateway.admin_api_gateway_id
  description = "ID of the Staging API Gateway"
}  

