package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/resendlabs/resend-go"
)

const (
	Sender    = "<EMAIL>"
	Recipient = "<EMAIL>"
)

type EmailBody struct {
	Name        string `json:"name"`
	Email       string `json:"email"`
	Size        string `json:"size"`
	Animal      string `json:"animal"`
	Description string `json:"description"`
}

func ContactEmailHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	var body EmailBody
	err := json.Unmarshal([]byte(request.Body), &body)
	if err != nil {
		log.Printf("Error unmarshalling request body: %v", err)
		return events.APIGatewayProxyResponse{StatusCode: 400, Body: "Invalid request body"}, nil
	}

	log.Printf("Parsed request body: %+v", body)

	htmlBody := fmt.Sprintf("<p>Name: %s</p><p>Email: %s</p><p>Size: %s</p><p>Animal: %s</p><p>Description: %s</p>", body.Name, body.Email, body.Size, body.Animal, body.Description)

	apiKey := os.Getenv("RESEND_API_KEY")
	if apiKey == "" {
		log.Println("RESEND_API_KEY is not set")
		return events.APIGatewayProxyResponse{StatusCode: 500, Body: "Internal Server Error"}, nil
	}

	log.Println("Sending email using Resend")

	client := resend.NewClient(apiKey)

	params := &resend.SendEmailRequest{
		From:    Sender,
		To:      []string{Recipient},
		Subject: "New Scratchboard Request",
		Html:    htmlBody,
	}

	response, err := client.Emails.Send(params)
	if err != nil {
		log.Printf("Error sending email: %v", err)
		return events.APIGatewayProxyResponse{StatusCode: 500, Body: "Internal Server Error"}, nil
	}

	log.Printf("Email sent successfully: %v", response)

	headers := map[string]string{
		"Access-Control-Allow-Origin":      "*",
		"Access-Control-Allow-Methods":     "OPTIONS,POST,GET",
		"Access-Control-Allow-Headers":     "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
		"Access-Control-Allow-Credentials": "true",
	}

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers:    headers,
		Body:       "Email sent successfully",
	}, nil
}

func main() {
	lambda.Start(ContactEmailHandler)
}
