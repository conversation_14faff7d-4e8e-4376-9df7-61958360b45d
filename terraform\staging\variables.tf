variable "environment" {
  type    = string
  default = "test"
  
}

variable "region" {
  type    = string
  default = "us-west-2"
}

variable "account_id"{
  type        = string
  description = "The account ID in which to create/manage resources"
}

variable "access_key" {
  type = string
  description = "AWS access key"
}

variable "secret_key" {
  type = string
  description = "AWS secret access key"
}

variable "zone_id" {
  description = "The Zone ID of the Route53 zone"
  type        = string
}

variable "domain_name" {
  type        = string
  description = "The domain name for the website."
}

variable "bucket_name" {
  type        = string
  description = "The name of the bucket without the www. prefix. Normally domain_name."
}

variable "common_tags" {
  description = "Common tags you want applied to all components."
}

variable "api_gateway_id" {
  type = string
  description = "The ID of the api-gateway"
}

variable "api_gateway_region" {
  type = string
  description = "region"
}

variable "api_gateway_account_id" {
  type = string
  description = "account id"
}

variable "stripe_secret_key" {
  type = string
  description = "Stripe Secret Key"
}

variable "stripe_endpoint_secret" {
  type = string
  description = "Stripe Endpoint Secret"
}


variable "lambda_function_arn" {
  type = string
  description ="Lambda function ARN"
}

variable "lambda_function_checkout_arn" {
  description = "checkout function ARN"
  type        = string
}

variable "lambda_function_webhooks_arn" {
  description = "checkout function ARN"
  type        = string
}
variable "lambda_function_contact_arn" {
  description = "checkout function ARN"
  type        = string
}

