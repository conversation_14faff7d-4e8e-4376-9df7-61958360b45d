resource "aws_api_gateway_resource" "webhooks_resource" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  parent_id   = aws_api_gateway_rest_api.rest_api.root_resource_id
  path_part   = "dev-webhooks"
}
# POST method for webhooks
resource "aws_api_gateway_method" "webhooks_products_post_method" {
  rest_api_id   = aws_api_gateway_rest_api.rest_api.id
  resource_id   = aws_api_gateway_resource.webhooks_resource.id
  http_method   = "POST"
  authorization = "none"
}

# Integration for webhooks POST method
resource "aws_api_gateway_integration" "webhooks_products_post_integration" {
  rest_api_id             = aws_api_gateway_rest_api.rest_api.id
  resource_id             = aws_api_gateway_resource.webhooks_resource.id
  http_method             = aws_api_gateway_method.webhooks_products_post_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = var.lambda_function_webhooks_arn

}

resource "aws_api_gateway_method_response" "webhooks_post_method_response_200" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  resource_id = aws_api_gateway_resource.webhooks_resource.id
  http_method = aws_api_gateway_method.webhooks_products_post_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration_response" "webhooks_post_integration_response_200" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  resource_id = aws_api_gateway_resource.webhooks_resource.id
  http_method = aws_api_gateway_integration.webhooks_products_post_integration.http_method
  status_code = aws_api_gateway_method_response.webhooks_post_method_response_200.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token, X-Requested-With, Stripe-Signature'"
    "method.response.header.Access-Control-Allow-Methods"     = "'OPTIONS,POST'"
    "method.response.header.Access-Control-Allow-Origin"      = "'https://test.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

resource "aws_lambda_permission" "webhooks_api_gw_permissions" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.webhooks_function}"
  action        = "lambda:InvokeFunction"
  function_name = var.webhooks_function
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.rest_api.id}/*/${aws_api_gateway_method.webhooks_products_post_method.http_method}${aws_api_gateway_resource.webhooks_resource.path}"
}
