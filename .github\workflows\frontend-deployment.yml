name: Frontend Deployment

on:
  push:
    branches: [dev, development]
    paths: ['src/**', 'public/**', 'package.json', 'package-lock.json']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - production

env:
  AWS_REGION: us-east-1
  NODE_VERSION: '18'

jobs:
  # Build frontend
  build-frontend:
    name: Build Frontend
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm ci

      - name: Set environment variables
        run: |
          if [ "${{ inputs.environment || 'development' }}" = "development" ]; then
            echo "REACT_APP_API_BASE_URL=https://api-dev.thealpinestudio.com" >> $GITHUB_ENV
            echo "REACT_APP_ENVIRONMENT=development" >> $GITHUB_ENV
            echo "CLOUDFRONT_DISTRIBUTION_ID=E333R3CHKXLYGZ" >> $GITHUB_ENV
            echo "FRONTEND_URL=https://test.thealpinestudio.com" >> $GITHUB_ENV
          else
            echo "REACT_APP_API_BASE_URL=https://api.thealpinestudio.com" >> $GITHUB_ENV
            echo "REACT_APP_ENVIRONMENT=production" >> $GITHUB_ENV
            echo "CLOUDFRONT_DISTRIBUTION_ID=ETN10ORXOMMDL" >> $GITHUB_ENV
            echo "FRONTEND_URL=https://thealpinestudio.com" >> $GITHUB_ENV
          fi

      - name: Build frontend
        run: npm run build
        env:
          REACT_APP_API_BASE_URL: ${{ env.REACT_APP_API_BASE_URL }}
          REACT_APP_ENVIRONMENT: ${{ env.REACT_APP_ENVIRONMENT }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: frontend-build
          path: build/
          retention-days: 7

  # Deploy to S3 and CloudFront
  deploy-frontend:
    name: Deploy to S3 & CloudFront
    runs-on: ubuntu-latest
    needs: [build-frontend]
    environment: ${{ inputs.environment || 'development' }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Set environment variables
        run: |
          if [ "${{ inputs.environment || 'development' }}" = "development" ]; then
            echo "CLOUDFRONT_DISTRIBUTION_ID=E333R3CHKXLYGZ" >> $GITHUB_ENV
            echo "FRONTEND_URL=https://test.thealpinestudio.com" >> $GITHUB_ENV
          else
            echo "CLOUDFRONT_DISTRIBUTION_ID=ETN10ORXOMMDL" >> $GITHUB_ENV
            echo "FRONTEND_URL=https://thealpinestudio.com" >> $GITHUB_ENV
          fi

      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: frontend-build
          path: ./build

      - name: Get S3 bucket from CloudFront
        id: s3-bucket
        run: |
          BUCKET_DOMAIN=$(aws cloudfront get-distribution --id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} --query 'Distribution.DistributionConfig.Origins.Items[0].DomainName' --output text)
          BUCKET_NAME=$(echo $BUCKET_DOMAIN | sed 's/.s3.amazonaws.com//' | sed 's/.s3.[^.]*\.amazonaws.com//')
          echo "bucket_name=$BUCKET_NAME" >> $GITHUB_OUTPUT
          echo "Deploying to S3 bucket: $BUCKET_NAME"

      - name: Sync files to S3
        run: |
          echo "Syncing files to S3..."
          
          # Sync static assets with long cache
          aws s3 sync ./build s3://${{ steps.s3-bucket.outputs.bucket_name }} \
            --delete \
            --cache-control "public, max-age=31536000" \
            --exclude "*.html" \
            --exclude "service-worker.js" \
            --exclude "manifest.json"
          
          # Sync HTML files with no cache
          aws s3 sync ./build s3://${{ steps.s3-bucket.outputs.bucket_name }} \
            --delete \
            --cache-control "public, max-age=0, must-revalidate" \
            --include "*.html"
          
          # Sync service worker and manifest with short cache
          aws s3 sync ./build s3://${{ steps.s3-bucket.outputs.bucket_name }} \
            --delete \
            --cache-control "public, max-age=3600" \
            --include "service-worker.js" \
            --include "manifest.json"

      - name: Invalidate CloudFront
        run: |
          echo "Creating CloudFront invalidation..."
          INVALIDATION_ID=$(aws cloudfront create-invalidation \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/*" \
            --query 'Invalidation.Id' \
            --output text)
          
          echo "Invalidation created: $INVALIDATION_ID"
          echo "invalidation_id=$INVALIDATION_ID" >> $GITHUB_OUTPUT

      - name: Wait for invalidation
        run: |
          echo "Waiting for CloudFront invalidation to complete..."
          aws cloudfront wait invalidation-completed \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --id ${{ steps.invalidate.outputs.invalidation_id }} || echo "Invalidation wait timed out, but deployment likely succeeded"

  # Verify deployment
  verify-deployment:
    name: Verify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-frontend]
    
    steps:
      - name: Set environment variables
        run: |
          if [ "${{ inputs.environment || 'development' }}" = "development" ]; then
            echo "FRONTEND_URL=https://test.thealpinestudio.com" >> $GITHUB_ENV
          else
            echo "FRONTEND_URL=https://thealpinestudio.com" >> $GITHUB_ENV
          fi

      - name: Wait for deployment to propagate
        run: sleep 30

      - name: Health check
        run: |
          echo "Checking frontend health at ${{ env.FRONTEND_URL }}"
          
          # Try multiple times as CloudFront may take time to propagate
          for i in {1..5}; do
            response=$(curl -s -o /dev/null -w "%{http_code}" ${{ env.FRONTEND_URL }})
            if [ $response -eq 200 ]; then
              echo "✅ Frontend is healthy (HTTP $response)"
              exit 0
            else
              echo "Attempt $i: HTTP $response, retrying in 10 seconds..."
              sleep 10
            fi
          done
          
          echo "❌ Frontend health check failed after 5 attempts"
          exit 1

      - name: Check for environment indicator
        if: inputs.environment == 'development' || github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/development'
        run: |
          echo "Checking for development environment indicator..."
          content=$(curl -s ${{ env.FRONTEND_URL }})
          if echo "$content" | grep -q "development\|test"; then
            echo "✅ Development environment indicator found"
          else
            echo "⚠️  Development environment indicator not found (this may be normal)"
          fi

  # Create deployment tag
  create-tag:
    name: Create Deployment Tag
    runs-on: ubuntu-latest
    needs: [verify-deployment]
    if: github.event_name == 'push'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create and push tag
        run: |
          ENVIRONMENT="${{ inputs.environment || 'development' }}"
          TIMESTAMP=$(date +%Y%m%d-%H%M%S)
          TAG_NAME="frontend-${ENVIRONMENT}-${TIMESTAMP}"
          
          echo "Creating tag: $TAG_NAME"
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git tag -a "$TAG_NAME" -m "Frontend deployment to $ENVIRONMENT environment"
          git push origin "$TAG_NAME"

  # Send notifications
  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    needs: [verify-deployment, create-tag]
    if: always()
    
    steps:
      - name: Set environment variables
        run: |
          if [ "${{ inputs.environment || 'development' }}" = "development" ]; then
            echo "FRONTEND_URL=https://test.thealpinestudio.com" >> $GITHUB_ENV
            echo "ENVIRONMENT_NAME=Development" >> $GITHUB_ENV
          else
            echo "FRONTEND_URL=https://thealpinestudio.com" >> $GITHUB_ENV
            echo "ENVIRONMENT_NAME=Production" >> $GITHUB_ENV
          fi

      - name: Determine deployment status
        id: status
        run: |
          if [ "${{ needs.verify-deployment.result }}" = "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "message=✅ Frontend deployment to ${{ env.ENVIRONMENT_NAME }} completed successfully!" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "message=❌ Frontend deployment to ${{ env.ENVIRONMENT_NAME }} failed!" >> $GITHUB_OUTPUT
          fi

      - name: Send Slack notification
        if: env.SLACK_WEBHOOK_URL != ''
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ steps.status.outputs.status }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          message: |
            ${{ steps.status.outputs.message }}
            
            🌐 Environment: ${{ env.ENVIRONMENT_NAME }}
            🔗 URL: ${{ env.FRONTEND_URL }}
            📝 Commit: ${{ github.sha }}
            👤 Author: ${{ github.actor }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
