name: Frontend Deployment

on:
  push:
    branches: [dev, development]
    paths:
      [
        "client/src/**",
        "client/public/**",
        "client/package.json",
        "client/package-lock.json",
      ]
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy to"
        required: true
        default: "development"
        type: choice
        options:
          - development
          - production

env:
  AWS_REGION: us-east-1
  NODE_VERSION: "20"

jobs:
  deploy-frontend:
    name: Build & Deploy Frontend
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"
          cache-dependency-path: "client/package-lock.json"

      - name: Install dependencies
        run: npm ci
        working-directory: ./client

      - name: Configure AWS credentials for secrets
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Get secrets from AWS Secrets Manager
        id: get-secrets
        run: |
          ENVIRONMENT="${{ inputs.environment || 'development' }}"

          if [ "$ENVIRONMENT" = "development" ]; then
            # Get API routes from Secrets Manager
            API_ROUTES_JSON=$(aws secretsmanager get-secret-value \
              --secret-id "dev/frontend/api-routes" \
              --query SecretString \
              --output text \
              --region us-west-2 2>/dev/null || echo "")

            # Get Stripe publishable key from Secrets Manager
            STRIPE_KEY=$(aws secretsmanager get-secret-value \
              --secret-id "dev/stripe/publishable-key" \
              --query SecretString \
              --output text \
              --region us-west-2 2>/dev/null || echo "")

            # Parse API routes or use fallbacks
            if [ -n "$API_ROUTES_JSON" ]; then
              echo "✅ Using API routes from Secrets Manager"
              CART_API_ROUTE=$(echo "$API_ROUTES_JSON" | jq -r '.cart_api_route')
              PRODUCT_API_ROUTE=$(echo "$API_ROUTES_JSON" | jq -r '.product_api_route')
              CONTACT_API_ROUTE=$(echo "$API_ROUTES_JSON" | jq -r '.contact_api_route')
              WEBHOOKS_API_ROUTE=$(echo "$API_ROUTES_JSON" | jq -r '.webhooks_api_route')
            else
              echo "⚠️ Using fallback API routes"
              API_BASE="https://9ubhtxv696.execute-api.us-west-2.amazonaws.com/test"
              CART_API_ROUTE="$API_BASE/dev-checkout-products"
              PRODUCT_API_ROUTE="$API_BASE/dev-list-products"
              CONTACT_API_ROUTE="$API_BASE/dev-contact-email"
              WEBHOOKS_API_ROUTE="$API_BASE/dev-webhooks"
            fi

            # Handle Stripe key
            if [ -z "$STRIPE_KEY" ]; then
              STRIPE_KEY="${{ secrets.STRIPE_PUBLISHABLE_KEY }}"
              echo "⚠️ Using Stripe key from GitHub secrets"
            else
              echo "✅ Using Stripe key from Secrets Manager"
            fi

            # Set environment variables for React build
            echo "REACT_APP_CART_API_ROUTE=$CART_API_ROUTE" >> $GITHUB_ENV
            echo "REACT_APP_PRODUCT_API_ROUTE=$PRODUCT_API_ROUTE" >> $GITHUB_ENV
            echo "REACT_APP_CONTACT_API_ROUTE=$CONTACT_API_ROUTE" >> $GITHUB_ENV
            echo "REACT_APP_WEBHOOKS_API_ROUTE=$WEBHOOKS_API_ROUTE" >> $GITHUB_ENV
            echo "REACT_APP_ENVIRONMENT=development" >> $GITHUB_ENV
            echo "REACT_APP_PUBLIC_KEY=$STRIPE_KEY" >> $GITHUB_ENV
            echo "REACT_APP_S3_BUCKET=test.thealpinestudio.com-v1" >> $GITHUB_ENV
            echo "S3_BUCKET=test.thealpinestudio.com-v1" >> $GITHUB_ENV
            echo "CLOUDFRONT_DISTRIBUTION_ID=E333R3CHKXLYGZ" >> $GITHUB_ENV
          else
            # Production environment
            STRIPE_KEY=$(aws secretsmanager get-secret-value \
              --secret-id "prod/stripe/publishable-key" \
              --query SecretString \
              --output text \
              --region us-west-2 2>/dev/null || echo "${{ secrets.STRIPE_PUBLISHABLE_KEY }}")

            echo "REACT_APP_CART_API_ROUTE=https://api.thealpinestudio.com/checkout-products" >> $GITHUB_ENV
            echo "REACT_APP_PRODUCT_API_ROUTE=https://api.thealpinestudio.com/list-products" >> $GITHUB_ENV
            echo "REACT_APP_CONTACT_API_ROUTE=https://api.thealpinestudio.com/contact-email" >> $GITHUB_ENV
            echo "REACT_APP_WEBHOOKS_API_ROUTE=https://api.thealpinestudio.com/webhooks" >> $GITHUB_ENV
            echo "REACT_APP_ENVIRONMENT=production" >> $GITHUB_ENV
            echo "REACT_APP_PUBLIC_KEY=$STRIPE_KEY" >> $GITHUB_ENV
            echo "REACT_APP_S3_BUCKET=thealpinestudio.com-v1" >> $GITHUB_ENV
            echo "S3_BUCKET=thealpinestudio.com-v1" >> $GITHUB_ENV
            echo "CLOUDFRONT_DISTRIBUTION_ID=ETN10ORXOMMDL" >> $GITHUB_ENV
          fi

      - name: Build frontend
        run: npm run build
        working-directory: ./client

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Deploy to S3
        run: |
          echo "Deploying to S3 bucket: ${{ env.S3_BUCKET }}"
          aws s3 cp ./client/build/ s3://${{ env.S3_BUCKET }}/ --recursive

      - name: Invalidate CloudFront
        run: |
          echo "Invalidating CloudFront distribution: ${{ env.CLOUDFRONT_DISTRIBUTION_ID }}"
          aws cloudfront create-invalidation \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/*"

      - name: Verify deployment
        run: |
          ENVIRONMENT="${{ inputs.environment || 'development' }}"
          echo "✅ Frontend deployed successfully!"
          echo "🌐 Environment: $ENVIRONMENT"
          echo "📦 S3 Bucket: ${{ env.S3_BUCKET }}"
          echo "🔗 CloudFront: ${{ env.CLOUDFRONT_DISTRIBUTION_ID }}"
