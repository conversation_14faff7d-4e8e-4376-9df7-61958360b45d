name: Frontend Deployment

on:
  push:
    branches: [dev, development]
    paths:
      [
        "client/src/**",
        "client/public/**",
        "client/package.json",
        "client/package-lock.json",
      ]
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy to"
        required: true
        default: "development"
        type: choice
        options:
          - development
          - production

env:
  AWS_REGION: us-east-1
  NODE_VERSION: "20"

jobs:
  deploy-frontend:
    name: Build & Deploy Frontend
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"
          cache-dependency-path: "client/package-lock.json"

      - name: Install dependencies
        run: npm ci
        working-directory: ./client

      - name: Set environment variables
        run: |
          ENVIRONMENT="${{ inputs.environment || 'development' }}"
          if [ "$ENVIRONMENT" = "development" ]; then
            # Use API Gateway URL from infrastructure deployment, fallback to hardcoded if not available
            API_URL="${{ vars.DEV_API_GATEWAY_URL }}"
            if [ -z "$API_URL" ]; then
              API_URL="https://api-dev.thealpinestudio.com"
              echo "⚠️ Using fallback API URL: $API_URL"
            else
              echo "✅ Using Terraform API Gateway URL: $API_URL"
            fi
            echo "REACT_APP_API_BASE_URL=$API_URL" >> $GITHUB_ENV
            echo "REACT_APP_ENVIRONMENT=development" >> $GITHUB_ENV
            echo "REACT_APP_PUBLIC_KEY=${{ secrets.STRIPE_PUBLISHABLE_KEY }}" >> $GITHUB_ENV
            echo "S3_BUCKET=test.thealpinestudio.com-v1" >> $GITHUB_ENV
            echo "CLOUDFRONT_DISTRIBUTION_ID=E333R3CHKXLYGZ" >> $GITHUB_ENV
          else
            echo "REACT_APP_API_BASE_URL=https://api.thealpinestudio.com" >> $GITHUB_ENV
            echo "REACT_APP_ENVIRONMENT=production" >> $GITHUB_ENV
            echo "REACT_APP_PUBLIC_KEY=${{ secrets.STRIPE_PUBLISHABLE_KEY }}" >> $GITHUB_ENV
            echo "S3_BUCKET=thealpinestudio.com-v1" >> $GITHUB_ENV
            echo "CLOUDFRONT_DISTRIBUTION_ID=ETN10ORXOMMDL" >> $GITHUB_ENV
          fi

      - name: Build frontend
        run: npm run build
        working-directory: ./client

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Deploy to S3
        run: |
          echo "Deploying to S3 bucket: ${{ env.S3_BUCKET }}"
          aws s3 cp ./client/build/ s3://${{ env.S3_BUCKET }}/ --recursive

      - name: Invalidate CloudFront
        run: |
          echo "Invalidating CloudFront distribution: ${{ env.CLOUDFRONT_DISTRIBUTION_ID }}"
          aws cloudfront create-invalidation \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/*"

      - name: Verify deployment
        run: |
          ENVIRONMENT="${{ inputs.environment || 'development' }}"
          echo "✅ Frontend deployed successfully!"
          echo "🌐 Environment: $ENVIRONMENT"
          echo "📦 S3 Bucket: ${{ env.S3_BUCKET }}"
          echo "🔗 CloudFront: ${{ env.CLOUDFRONT_DISTRIBUTION_ID }}"
