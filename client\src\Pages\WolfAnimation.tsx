import { Box } from '@mui/material'
import <PERSON> from '../Components/ScratchBoardAnimation.tsx/Wolf'
import React from 'react'

const WolfAnimation = ({ color }: { color: "primary" | "secondary" }) => {
  return (
    <div>WolfAnimation
        <Box
        sx={{
          width: "100vw",
          height: "auto",
          bgcolor: "black",
          alignItems: "center",
          display: "flex",
          justifyContent: "center",
          flexDirection: "column",
          textAlign: "center",
          pb: 10,
          pt: 10,
        }}
      >
        <Wolf />
      </Box>
    </div>
  )
}

export default WolfAnimation