#!/bin/bash

# Variables
BUCKET_NAME="admin-thealpinestudio-lambda-functions"
FILE_NAME="mark-product-As-sold.zip"
LAMBDA_FUNCTION_NAME="auth_mark_sold_product"
REGION="us-west-2"

# Build the Products Lambda Function
GOOS=linux GOARCH=amd64 go build -o bootstrap markSoldProducts.go
if [ $? -ne 0 ]; then
  echo "Build failed."
  exit 1
else
  echo "Build succeeded."
fi

# Zip the executable
zip $FILE_NAME bootstrap
if [ $? -ne 0 ]; then
  echo "Zip failed."
  exit 1
else
  echo "Zip succeeded."
fi

# Upload the zipped file to S3
aws s3 cp $FILE_NAME s3://$BUCKET_NAME/$FILE_NAME --region $REGION
if [ $? -ne 0 ]; then
  echo "S3 upload failed."
  exit 1
else
  echo "S3 upload succeeded."
fi

# Update the Lambda function with the code from S3
aws lambda update-function-code \
    --function-name $LAMBDA_FUNCTION_NAME \
    --s3-bucket $BUCKET_NAME \
    --s3-key $FILE_NAME \
    --region $REGION
if [ $? -ne 0 ]; then
  echo "Lambda function update failed."
  exit 1
else
  echo "Lambda function update succeeded."
fi
