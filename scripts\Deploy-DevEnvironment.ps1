# PowerShell script to deploy the development environment

# Set the correct AWS profile
$env:AWS_PROFILE = "thealpinestudio"

# Verify AWS identity
Write-Host "Using AWS identity:"
aws sts get-caller-identity

# Configuration
$REGION = "us-west-2"
$TERRAFORM_STATE_BUCKET = "dev-thealpinestudio-backend-tf"
$LAMBDA_BUCKET = "dev-thealpinestudio-lambda-functions-v1"
$HOSTING_BUCKET = "dev-thealpinestudio-hosting-v1"
$ACCOUNT_ID = "************"  # Your AWS account ID

Write-Host "=== Starting Development Environment Deployment ==="

# Step 1: Create required S3 buckets
Write-Host "Creating S3 buckets..."
try {
    aws s3api head-bucket --bucket $TERRAFORM_STATE_BUCKET 2>$null
} catch {
    Write-Host "Creating Terraform state bucket..."
    aws s3 mb "s3://$TERRAFORM_STATE_BUCKET" --region $REGION
}

try {
    aws s3api head-bucket --bucket $LAMBDA_BUCKET 2>$null
    Write-Host "Lambda bucket exists."
} catch {
    Write-Host "Creating Lambda bucket..."
    aws s3 mb "s3://$LAMBDA_BUCKET" --region $REGION
}

# Step 2: Disable Block Public Access for hosting bucket (if needed)
Write-Host "Configuring hosting bucket..."
try {
    aws s3api head-bucket --bucket $HOSTING_BUCKET 2>$null
    Write-Host "Disabling Block Public Access for hosting bucket..."
    aws s3api put-public-access-block --bucket $HOSTING_BUCKET --public-access-block-configuration "BlockPublicAcls=false,IgnorePublicAcls=false,BlockPublicPolicy=false,RestrictPublicBuckets=false"
} catch {
    Write-Host "Hosting bucket doesn't exist or you don't have access to it."
}

# Step 3: Build and upload Lambda functions
Write-Host "Building and uploading Lambda functions..."

# Function to build and upload a Lambda function
function Build-UploadLambda {
    param (
        [string]$Directory,
        [string]$SourceFile,
        [string]$OutputFile,
        [string]$ZipFile,
        [string]$S3Key
    )

    Write-Host "Building $ZipFile..."
    Push-Location $Directory

    $env:GOOS = "linux"
    $env:GOARCH = "amd64"
    go build -o $OutputFile $SourceFile

    Compress-Archive -Path $OutputFile -DestinationPath $ZipFile -Force

    aws s3 cp $ZipFile "s3://$LAMBDA_BUCKET/$S3Key" --region $REGION

    Pop-Location
}

# Build and upload all Lambda functions
Build-UploadLambda -Directory "lambda/dev/ListProducts" -SourceFile "listProducts.go" -OutputFile "bootstrap" -ZipFile "list-products.zip" -S3Key "list-products.zip"
Build-UploadLambda -Directory "lambda/dev/CheckoutProducts" -SourceFile "checkoutProducts.go" -OutputFile "bootstrap" -ZipFile "checkout-products.zip" -S3Key "checkout-products.zip"
Build-UploadLambda -Directory "lambda/dev/ContactEmail" -SourceFile "contactEmail.go" -OutputFile "bootstrap" -ZipFile "contact-email.zip" -S3Key "contact-email.zip"
Build-UploadLambda -Directory "lambda/dev/Webhooks" -SourceFile "webhooks.go" -OutputFile "bootstrap" -ZipFile "webhooks.zip" -S3Key "webhooks.zip"

# Step 4: Create Lambda functions directly with AWS CLI
Write-Host "Creating Lambda functions..."

# Create Lambda execution role if it doesn't exist
$LAMBDA_ROLE_NAME = "lambda-execution-role"
$LAMBDA_ROLE_ARN = "arn:aws:iam::$ACCOUNT_ID`:role/$LAMBDA_ROLE_NAME"

Write-Host "Creating Lambda execution role..."
try {
    aws iam get-role --role-name $LAMBDA_ROLE_NAME 2>$null
    Write-Host "Lambda execution role already exists."
} catch {
    Write-Host "Creating new Lambda execution role..."
    aws iam create-role `
        --role-name $LAMBDA_ROLE_NAME `
        --assume-role-policy-document '{"Version":"2012-10-17","Statement":[{"Effect":"Allow","Principal":{"Service":"lambda.amazonaws.com"},"Action":"sts:AssumeRole"}]}' `
        --region $REGION

    aws iam attach-role-policy `
        --role-name $LAMBDA_ROLE_NAME `
        --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole `
        --region $REGION

    # Wait for role to propagate
    Write-Host "Waiting for role to propagate..."
    Start-Sleep -Seconds 10
}

# Function to create or update a Lambda function
function Create-UpdateLambda {
    param (
        [string]$FunctionName,
        [string]$S3Key
    )

    Write-Host "Checking Lambda function: $FunctionName..."

    try {
        aws lambda get-function --function-name $FunctionName --region $REGION 2>$null
        Write-Host "Updating existing Lambda function: $FunctionName..."
        aws lambda update-function-code `
            --function-name $FunctionName `
            --s3-bucket $LAMBDA_BUCKET `
            --s3-key $S3Key `
            --region $REGION
    } catch {
        Write-Host "Creating new Lambda function: $FunctionName..."
        aws lambda create-function `
            --function-name $FunctionName `
            --runtime provided.al2 `
            --handler bootstrap `
            --role $LAMBDA_ROLE_ARN `
            --s3-bucket $LAMBDA_BUCKET `
            --s3-key $S3Key `
            --region $REGION
    }
}

# Create or update Lambda functions
Create-UpdateLambda -FunctionName "dev-list-products" -S3Key "list-products.zip"
Create-UpdateLambda -FunctionName "dev-checkout-products" -S3Key "checkout-products.zip"
Create-UpdateLambda -FunctionName "dev-contact-email" -S3Key "contact-email.zip"
Create-UpdateLambda -FunctionName "dev-webhooks" -S3Key "webhooks.zip"

# Step 5: Apply Terraform
Write-Host "Applying Terraform..."
Push-Location terraform\development\dev-env

# Initialize Terraform
Write-Host "Initializing Terraform..."
terraform init

# Import existing resources
Write-Host "Importing existing resources..."

# Import IAM Role
Write-Host "Importing IAM Role: testing-Cognito_DefaultUnauthenticatedRole"
try { terraform import module.cognito.aws_iam_role.unauth_role testing-Cognito_DefaultUnauthenticatedRole } catch { Write-Host "Import failed, continuing..." }

# Import Route 53 Record
Write-Host "Importing Route 53 Record: test.thealpinestudio.com"
$ZONE_ID = (aws route53 list-hosted-zones --query "HostedZones[?Name=='thealpinestudio.com.'].Id" --output text) -replace '/hostedzone/', ''

# Make sure we have a zone ID before proceeding
if ($ZONE_ID) {
    Write-Host "Found Zone ID: $ZONE_ID"

    # Try to import the record
    Write-Host "Importing Route 53 record..."
    terraform import module.website_s3_bucket.aws_route53_record.sub_domain ${ZONE_ID}_test.thealpinestudio.com_A

    # Check if import was successful
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Successfully imported Route 53 record."
    } else {
        Write-Host "Failed to import Route 53 record. You may need to modify the Terraform configuration."

        # Offer to modify the Terraform configuration
        $modify = Read-Host "Would you like to modify the Terraform configuration to skip creating this record? (y/n)"
        if ($modify -eq "y") {
            # Find the file containing the Route 53 record definition
            $file = "..\dev-modules\dev-s3_hosting\s3_hosting.tf"

            # Check if the file exists
            if (Test-Path $file) {
                # Read the file content
                $content = Get-Content $file -Raw

                # Add a comment to the resource definition
                $content = $content -replace 'resource "aws_route53_record" "sub_domain" {', '# This record already exists and has been imported
resource "aws_route53_record" "sub_domain" {'

                # Write the modified content back to the file
                Set-Content $file $content

                Write-Host "Terraform configuration updated."
            } else {
                Write-Host "Could not find the file containing the Route 53 record definition."
            }
        }
    }
} else {
    Write-Host "Could not find Zone ID for thealpinestudio.com"
}

# Import Lambda functions
Write-Host "Importing Lambda functions..."
try { terraform import module.lambda_function.aws_lambda_function.list_products dev-list-products } catch { Write-Host "Import failed, continuing..." }
try { terraform import module.lambda_function.aws_lambda_function.checkout_products_function dev-checkout-products } catch { Write-Host "Import failed, continuing..." }
try { terraform import module.lambda_function.aws_lambda_function.contact_email_function dev-contact-email } catch { Write-Host "Import failed, continuing..." }
try { terraform import module.lambda_function.aws_lambda_function.webhooks_function dev-webhooks } catch { Write-Host "Import failed, continuing..." }

# Apply Terraform in stages
Write-Host "Applying Lambda functions..."
terraform apply -target=module.lambda_function -auto-approve

Write-Host "Applying remaining resources..."
terraform apply -auto-approve

# Step 6: Build and deploy frontend
Write-Host "Building and deploying frontend..."
Pop-Location
Push-Location client

# Install dependencies
npm install

# Build frontend
npm run build:dev

# Deploy to S3
aws s3 cp build/ "s3://$HOSTING_BUCKET/" --recursive --region $REGION

# Invalidate CloudFront cache
aws cloudfront create-invalidation --distribution-id E333R3CHKXLYGZ --paths "/*" --region $REGION

Pop-Location

Write-Host "=== Development Environment Deployment Complete ==="
