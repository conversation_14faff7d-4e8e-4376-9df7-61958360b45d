package main

import (
	"context"
	"fmt"
	"log"

	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
)

type VerifyAuthChallengeRequest struct {
	Request struct {
		UserAttributes  map[string]string `json:"userAttributes"`
		ChallengeAnswer string            `json:"challengeAnswer"`
	} `json:"request"`
	Response struct {
		AnswerCorrect bool `json:"answerCorrect"`
	} `json:"response"`
}

func handleVerifyAuthChallenge(ctx context.Context, event VerifyAuthChallengeRequest) (VerifyAuthChallengeRequest, error) {
	log.Printf("Received event: %+v", event)

	email := event.Request.UserAttributes["email"]
	mfaCode := event.Request.ChallengeAnswer

	// Retrieve the MFA code from DynamoDB
	correctCode, err := retrieveMFACodeFromDynamoDB(email)
	if err != nil {
		return event, fmt.Errorf("failed to retrieve MFA code: %v", err)
	}

	event.Response.AnswerCorrect = (mfaCode == correctCode)

	log.Printf("Returning response: %+v", event)
	return event, nil
}

func retrieveMFACodeFromDynamoDB(email string) (string, error) {
	sess, err := session.NewSession(&aws.Config{Region: aws.String("us-west-2")})
	if err != nil {
		return "", err
	}

	svc := dynamodb.New(sess)

	input := &dynamodb.GetItemInput{
		TableName: aws.String("mfa_codes"),
		Key: map[string]*dynamodb.AttributeValue{
			"email": {
				S: aws.String(email),
			},
		},
	}

	result, err := svc.GetItem(input)
	if err != nil {
		return "", err
	}

	if result.Item == nil {
		return "", fmt.Errorf("no MFA code found for email %s", email)
	}

	var item struct {
		MFACode string `dynamodb:"mfa_code"`
	}
	err = dynamodbattribute.UnmarshalMap(result.Item, &item)
	if err != nil {
		return "", err
	}

	return item.MFACode, nil
}

func main() {
	lambda.Start(handleVerifyAuthChallenge)
}
