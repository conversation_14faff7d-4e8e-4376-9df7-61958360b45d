output "admin_user_pool_client_id" {
  value       = aws_cognito_user_pool_client.admin_user_pool_client.id
  description = "The Client ID of the Cognito User Pool Client"
}

output "cognito_admin_user_pool_arn" {
  value       = aws_cognito_user_pool.admin_user_pool_v2.arn
  description = "The ARN of the Cognito User Pool"
}

output "admin_user_pool_id" {
  value       = aws_cognito_user_pool.admin_user_pool_v2.id
  description = "The ID of the Cognito User Pool"
}

output "admin_user_pool_endpoint" {
  description = "User Pool Endpoint"
  value       = aws_cognito_user_pool.admin_user_pool_v2.endpoint
}

output "pre_sign_up_lambda_arn" {
  value = var.auth_cognito_pre_signup_function_arn
  description = "The ARN of the Lambda function that handles pre sign up"

}


output "sns_topic_name" {
  value = aws_sns_topic.cognito_sns_topic.name
  description = "The name of the SNS topic for Cognito"
  
}

# output "define_auth_challenge_lambda_arn" {
#   value = var.define_auth_challenge_lambda_arn
#   description = "The ARN of the Lambda function that handles define auth challenge"
  
# }



# output "custom_message_lambda_arn" {
#   value = var.custom_message_lambda_arn
#   description = "The ARN of the Lambda function that handles custom message"
# }


# output "post_authentication_lambda_arn" {
#   value = var.post_authentication_lambda_arn
#   description = "The ARN of the Lambda function that handles post authentication"

# }

# output "post_confirmation_lambda_arn" {
#   value = var.post_confirmation_lambda_arn
#   description = "The ARN of the Lambda function that handles post confirmation"

# }

# output "pre_authentication_lambda_arn" {
#   value = var.pre_authentication_lambda_arn
#   description = "The ARN of the Lambda function that handles pre authentication"

# }

# output "verify_auth_challenge_response_lambda_arn" {
#   value = var.verify_auth_challenge_response_lambda_arn
#   description = "The ARN of the Lambda function that handles verify auth challenge response"
# }

