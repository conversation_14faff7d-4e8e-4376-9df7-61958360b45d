name: Infrastructure Deployment

on:
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to deploy to"
        required: true
        default: "development"
        type: choice
        options:
          - development
          - production
      action:
        description: "Terraform action"
        required: true
        default: "apply"
        type: choice
        options:
          - plan
          - apply
          - destroy

env:
  AWS_REGION: us-east-1
  TERRAFORM_VERSION: "1.6.0"

jobs:
  # Terraform plan
  terraform-plan:
    name: Terraform Plan
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}

    outputs:
      plan_exitcode: ${{ steps.plan.outputs.exitcode }}
      plan_output: ${{ steps.plan.outputs.stdout }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Cache Terraform providers
        uses: actions/cache@v3
        with:
          path: ~/.terraform.d/plugin-cache
          key: ${{ runner.os }}-terraform-${{ hashFiles('**/.terraform.lock.hcl') }}

      - name: Set environment variables
        run: |
          ENVIRONMENT="${{ inputs.environment || 'development' }}"
          echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV

          if [ "$ENVIRONMENT" = "development" ]; then
            echo "TF_STATE_BUCKET=dev-thealpinestudio-backend-tf" >> $GITHUB_ENV
            echo "TF_STATE_KEY=development/terraform.tfstate" >> $GITHUB_ENV
            echo "TF_WORKSPACE=development" >> $GITHUB_ENV
          else
            echo "TF_STATE_BUCKET=thealpinestudio-backend-tf" >> $GITHUB_ENV
            echo "TF_STATE_KEY=production/terraform.tfstate" >> $GITHUB_ENV
            echo "TF_WORKSPACE=production" >> $GITHUB_ENV
          fi

      - name: Terraform Init
        run: |
          terraform init \
            -backend-config="bucket=${{ env.TF_STATE_BUCKET }}" \
            -backend-config="key=${{ env.TF_STATE_KEY }}" \
            -backend-config="region=${{ env.AWS_REGION }}"
        working-directory: ./terraform/${{ env.ENVIRONMENT }}

      - name: Terraform Workspace
        run: |
          terraform workspace select ${{ env.TF_WORKSPACE }} || terraform workspace new ${{ env.TF_WORKSPACE }}
        working-directory: ./terraform/${{ env.ENVIRONMENT }}

      - name: Terraform Plan
        id: plan
        run: |
          if [ "${{ inputs.action || 'apply' }}" = "destroy" ]; then
            terraform plan -destroy -detailed-exitcode -out=tfplan
          else
            terraform plan -detailed-exitcode -out=tfplan
          fi
        working-directory: ./terraform/${{ env.ENVIRONMENT }}
        env:
          TF_VAR_aws_profile: thealpinestudio
          TF_VAR_environment: ${{ env.ENVIRONMENT }}

      - name: Upload Terraform plan
        uses: actions/upload-artifact@v3
        with:
          name: terraform-plan-${{ env.ENVIRONMENT }}
          path: terraform/${{ env.ENVIRONMENT }}/tfplan
          retention-days: 7

      - name: Comment PR with plan
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const output = `
            ## Terraform Plan Results for ${{ env.ENVIRONMENT }}

            **Plan Exit Code:** ${{ steps.plan.outputs.exitcode }}

            <details>
            <summary>Show Plan Output</summary>

            \`\`\`
            ${{ steps.plan.outputs.stdout }}
            \`\`\`

            </details>
            `;

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: output
            })

  # Terraform apply (only runs if plan shows changes and it's not a PR)
  terraform-apply:
    name: Terraform Apply
    runs-on: ubuntu-latest
    needs: [terraform-plan]
    if: |
      github.event_name != 'pull_request' && 
      (inputs.action == 'apply' || inputs.action == 'destroy' || (inputs.action == '' && needs.terraform-plan.outputs.plan_exitcode == '2'))
    environment: ${{ inputs.environment || 'development' }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Set environment variables
        run: |
          ENVIRONMENT="${{ inputs.environment || 'development' }}"
          echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV

          if [ "$ENVIRONMENT" = "development" ]; then
            echo "TF_STATE_BUCKET=dev-thealpinestudio-backend-tf" >> $GITHUB_ENV
            echo "TF_STATE_KEY=development/terraform.tfstate" >> $GITHUB_ENV
            echo "TF_WORKSPACE=development" >> $GITHUB_ENV
          else
            echo "TF_STATE_BUCKET=thealpinestudio-backend-tf" >> $GITHUB_ENV
            echo "TF_STATE_KEY=production/terraform.tfstate" >> $GITHUB_ENV
            echo "TF_WORKSPACE=production" >> $GITHUB_ENV
          fi

      - name: Download Terraform plan
        uses: actions/download-artifact@v3
        with:
          name: terraform-plan-${{ env.ENVIRONMENT }}
          path: terraform/${{ env.ENVIRONMENT }}/

      - name: Terraform Init
        run: |
          terraform init \
            -backend-config="bucket=${{ env.TF_STATE_BUCKET }}" \
            -backend-config="key=${{ env.TF_STATE_KEY }}" \
            -backend-config="region=${{ env.AWS_REGION }}"
        working-directory: ./terraform/${{ env.ENVIRONMENT }}

      - name: Terraform Workspace
        run: |
          terraform workspace select ${{ env.TF_WORKSPACE }} || terraform workspace new ${{ env.TF_WORKSPACE }}
        working-directory: ./terraform/${{ env.ENVIRONMENT }}

      - name: Terraform Apply
        run: terraform apply -auto-approve tfplan
        working-directory: ./terraform/${{ env.ENVIRONMENT }}

      - name: Get Terraform outputs
        id: outputs
        run: |
          echo "## Terraform Outputs" >> $GITHUB_STEP_SUMMARY
          terraform output >> $GITHUB_STEP_SUMMARY

          # Save specific outputs for other workflows
          if terraform output api_gateway_url >/dev/null 2>&1; then
            echo "api_gateway_url=$(terraform output -raw api_gateway_url)" >> $GITHUB_OUTPUT
          fi
          if terraform output lambda_bucket >/dev/null 2>&1; then
            echo "lambda_bucket=$(terraform output -raw lambda_bucket)" >> $GITHUB_OUTPUT
          fi
        working-directory: ./terraform/${{ env.ENVIRONMENT }}

  # Post-deployment verification
  verify-infrastructure:
    name: Verify Infrastructure
    runs-on: ubuntu-latest
    needs: [terraform-apply]
    if: needs.terraform-apply.result == 'success'

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Set environment variables
        run: |
          ENVIRONMENT="${{ inputs.environment || 'development' }}"
          echo "ENVIRONMENT=$ENVIRONMENT" >> $GITHUB_ENV

      - name: Verify AWS resources
        run: |
          echo "Verifying infrastructure deployment..."

          # Check if API Gateway exists
          if [ "${{ env.ENVIRONMENT }}" = "development" ]; then
            API_NAME="dev-thealpinestudio-api"
          else
            API_NAME="thealpinestudio-api"
          fi

          if aws apigateway get-rest-apis --query "items[?name=='$API_NAME'].id" --output text | grep -q .; then
            echo "✅ API Gateway found: $API_NAME"
          else
            echo "⚠️  API Gateway not found: $API_NAME"
          fi

          # Check if Lambda functions bucket exists
          if [ "${{ env.ENVIRONMENT }}" = "development" ]; then
            BUCKET_NAME="dev-thealpinestudio-lambda-functions-v1"
          else
            BUCKET_NAME="thealpinestudio-lambda-functions-v1"
          fi

          if aws s3 ls "s3://$BUCKET_NAME" >/dev/null 2>&1; then
            echo "✅ Lambda functions bucket found: $BUCKET_NAME"
          else
            echo "⚠️  Lambda functions bucket not found: $BUCKET_NAME"
          fi

          # Check DynamoDB tables
          echo "Checking DynamoDB tables..."
          aws dynamodb list-tables --query 'TableNames' --output table | grep -E "(dev-|prod-)" || echo "No environment-specific tables found"

  # Create deployment tag
  create-tag:
    name: Create Deployment Tag
    runs-on: ubuntu-latest
    needs: [verify-infrastructure]
    if: github.event_name == 'push' && needs.verify-infrastructure.result == 'success'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create and push tag
        run: |
          ENVIRONMENT="${{ inputs.environment || 'development' }}"
          TIMESTAMP=$(date +%Y%m%d-%H%M%S)
          TAG_NAME="infrastructure-${ENVIRONMENT}-${TIMESTAMP}"

          echo "Creating tag: $TAG_NAME"
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git tag -a "$TAG_NAME" -m "Infrastructure deployment to $ENVIRONMENT environment"
          git push origin "$TAG_NAME"

  # Send notifications
  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    needs: [terraform-plan, terraform-apply, verify-infrastructure]
    if: always()

    steps:
      - name: Determine deployment status
        id: status
        run: |
          ENVIRONMENT="${{ inputs.environment || 'development' }}"

          if [ "${{ needs.terraform-apply.result }}" = "success" ] && [ "${{ needs.verify-infrastructure.result }}" = "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "message=✅ Infrastructure deployment to $ENVIRONMENT completed successfully!" >> $GITHUB_OUTPUT
          elif [ "${{ needs.terraform-plan.result }}" = "success" ] && [ "${{ github.event_name }}" = "pull_request" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "message=✅ Terraform plan completed for $ENVIRONMENT!" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "message=❌ Infrastructure deployment to $ENVIRONMENT failed!" >> $GITHUB_OUTPUT
          fi

      - name: Send Slack notification
        if: env.SLACK_WEBHOOK_URL != ''
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ steps.status.outputs.status }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          message: |
            ${{ steps.status.outputs.message }}

            🏗️ Environment: ${{ inputs.environment || 'development' }}
            🔧 Action: ${{ inputs.action || 'apply' }}
            📝 Commit: ${{ github.sha }}
            👤 Author: ${{ github.actor }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
