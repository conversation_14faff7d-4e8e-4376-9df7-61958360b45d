infra_env = "development"
access_key = "YOUR_AWS_ACCESS_KEY"
secret_key = "YOUR_AWS_SECRET_KEY"
account_id = "YOUR_AWS_ACCOUNT_ID"
zone_id = "YOUR_ROUTE53_ZONE_ID"
domain_name = "dev.thealpinestudio.com"
hosting_bucket_name = "dev-thealpinestudio-hosting"
list_products_function_name = "dev-list-products"
api_gateway_id = "YOUR_API_GATEWAY_ID"
api_gateway_account_id = "YOUR_AWS_ACCOUNT_ID"
api_gateway_region = "us-west-2"
stripe_secret_key = "YOUR_STRIPE_SECRET_KEY"
stripe_endpoint_secret = "YOUR_STRIPE_ENDPOINT_SECRET"
resend_api_key = "YOUR_RESEND_API_KEY"
frontend_url = "https://dev.thealpinestudio.com"
dynamodb_orders_table = "dev-orders"

# Add any other required variables here
