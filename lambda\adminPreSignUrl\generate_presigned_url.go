package main

import (
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "os"
    "time"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/s3"
)

// PresignedURLResponse holds the URL & key we return on success
type PresignedURLResponse struct {
    UploadURL string `json:"uploadUrl"`
    Key       string `json:"key"`
}

// ErrorResponse holds the message & data we return on error
type ErrorResponse struct {
    Message string `json:"message"`
    Data    string `json:"data"`
}

// handleRequest is our Lambda entrypoint
func handleRequest(request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    log.Println("[INFO] handleRequest invoked. Request:", request)
    
    sess, err := session.NewSession(&aws.Config{
        Region: aws.String("us-west-2"),
    })
    if err != nil {
        log.Printf("[ERROR] Failed to create AWS session: %v\n", err)
        return jsonResponse(http.StatusInternalServerError, ErrorResponse{
            Message: "Failed to create AWS session",
            Data:    "",
        })
    }

    s3Client := s3.New(sess)
    bucket := os.Getenv("IMAGE_BUCKET_NAME")
    if bucket == "" {
        log.Println("[ERROR] IMAGE_BUCKET_NAME environment variable is not set.")
        return jsonResponse(http.StatusInternalServerError, ErrorResponse{
            Message: "Image bucket name not set",
            Data:    "",
        })
    }

    // Generate a unique key for the image
    productID := fmt.Sprintf("%d", time.Now().Unix())
    key := fmt.Sprintf("originals/%s.jpg", productID)
    log.Printf("[INFO] Generating presigned URL for key: %s in bucket: %s\n", key, bucket)

    // Create a pre-signed PUT URL
    putObjReq, _ := s3Client.PutObjectRequest(&s3.PutObjectInput{
        Bucket:      aws.String(bucket),
        Key:         aws.String(key),
        ContentType: aws.String("image/jpeg"),
    })
    uploadURL, err := putObjReq.Presign(15 * time.Minute)
    if err != nil {
        log.Printf("[ERROR] Failed to generate presigned URL: %v\n", err)
        return jsonResponse(http.StatusInternalServerError, ErrorResponse{
            Message: "Failed to generate pre-signed URL",
            Data:    "",
        })
    }

    // Build our success response
    response := PresignedURLResponse{
        UploadURL: uploadURL,
        Key:       key,
    }
    return jsonResponse(http.StatusOK, response)
}

// jsonResponse is a helper to create a JSON payload with CORS headers
func jsonResponse(status int, body interface{}) (events.APIGatewayProxyResponse, error) {
    bodyBytes, err := json.Marshal(body)
    if err != nil {
        log.Printf("[ERROR] Failed to marshal response body: %v\n", err)
        // Return a minimal error response with CORS headers
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusInternalServerError,
            Body:       `{"message":"Internal Server Error"}`,
            Headers: map[string]string{
                "Content-Type":                    "application/json",
                "Access-Control-Allow-Origin":     "*",
                "Access-Control-Allow-Methods":    "OPTIONS,POST",
                "Access-Control-Allow-Headers":    "Content-Type,Authorization",
                "Access-Control-Allow-Credentials": "true",
            },
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: status,
        Body:       string(bodyBytes),
        Headers: map[string]string{
            "Content-Type":                    "application/json",
            "Access-Control-Allow-Origin":     "https://admin.thealpinestudio.com",
            "Access-Control-Allow-Methods":    "OPTIONS,POST",
            "Access-Control-Allow-Headers":    "Content-Type,Authorization",
            "Access-Control-Allow-Credentials": "true",
        },
    }, nil
}

func main() {
    log.Println("[INFO] Starting generate_presigned_url Lambda...")
    lambda.Start(handleRequest)
}
