# Safe Migration Strategy

## Current State (IMPORTANT)
- Production site (thealpinestudio.com) uses:
  - Lambda functions from /testing/test-modules/test-lambda/*
  - All resources prefixed with "test-" are actually PRODUCTION resources
  - Backend state in "testing-thealpinestudio-backend-tf" bucket
- test.thealpinestudio.com currently shares these production resources
- Problem: Cannot currently test changes safely as all resources are production

## Phase 1: Separate Production Code
1. Create new directory structure:
```
terraform/
├── production/           # Rename current "testing" directory to "production"
│   └── modules/         # Rename "test-modules" to "prod-modules"
│       └── lambda/      # Rename "testLambda" functions to "prodLambda"
├── testing/             # New ACTUAL testing environment
│   └── modules/         # New testing-specific modules
└── modules/             # Shared modules
```

2. Production Code Migration (NO RESOURCE CHANGES)
- [ ] Copy (don't rename) current /testing directory to /production
- [ ] Update module references but KEEP existing resource names
- [ ] Example of maintaining resource names:
```hcl
# Current (keep these names to avoid disruption):
function_name = "test-lambda-checkout"  # Despite "test" prefix, this is production
bucket_name = "test-thealpinestudio-lambda-functions-v1"  # Keep this name

# Just update directory structure and references
```

## Phase 2: Create New Testing Environment
1. Create Genuine Test Infrastructure
- [ ] Create new S3 bucket: `dev-thealpinestudio-terraform`
- [ ] Create new Lambda functions with "dev-" prefix:
  ```hcl
  # New testing resources (completely separate from production):
  function_name = "dev-lambda-checkout"
  bucket_name = "dev-thealpinestudio-lambda-functions-v1"
  ```
- [ ] Set up new API Gateway for testing
- [ ] Create new Cognito pool for testing
- [ ] Create new DynamoDB tables with "dev-" prefix

2. Update DNS for test.thealpinestudio.com
- [ ] Create new API endpoints for test.thealpinestudio.com
- [ ] Point test.thealpinestudio.com to new testing infrastructure
- [ ] Verify production (thealpinestudio.com) remains unchanged

## Phase 3: Testing Environment Configuration
1. Set Up Isolated Testing
- [ ] Configure CORS for test.thealpinestudio.com only
- [ ] Set up test data in new DynamoDB tables
- [ ] Create testing-specific environment variables

2. Validation Steps
- [ ] Verify production (thealpinestudio.com) still uses original resources
- [ ] Confirm test.thealpinestudio.com uses new dev resources
- [ ] Test all functionality in isolation

## Phase 4: Documentation
1. Clear Documentation of Resources
- [ ] Production Resources (currently with "test-" prefix):
  - List all Lambda functions, S3 buckets, etc. used by thealpinestudio.com
- [ ] New Testing Resources (with "dev-" prefix):
  - List all new resources used by test.thealpinestudio.com

2. Deployment Procedures
- [ ] Document production deployment process
- [ ] Document testing deployment process
- [ ] Create separate CI/CD pipelines

## Emergency Rollback Plan
1. DNS Rollback
- [ ] Keep DNS records for quick reversion
- [ ] Document steps to repoint test.thealpinestudio.com back to production if needed

2. State Management
- [ ] Maintain backups of both states
- [ ] Document rollback procedures



