package main

import (
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
	"github.com/aws/aws-sdk-go/service/dynamodb"
)

// Environment variables
var (
	clientID       = os.Getenv("CLIENT_ID")
	clientSecret   = os.Getenv("CLIENT_SECRET")
	region         = os.Getenv("AWS_REGION")
	usersTableName = os.Getenv("ADMIN_USERS")
	userPoolID     = os.Getenv("USER_POOL_ID")
)

type ConfirmSignUpRequest struct {
	Email            string `json:"email"`
	ConfirmationCode string `json:"confirmationCode"`
	Password         string `json:"password"`
	Username         string `json:"username"`
	PhoneNumber      string `json:"phone_number"`
}

type ConfirmSignUpResponse struct {
	Message    string `json:"message"`
	TOTPSecret string `json:"totp_secret,omitempty"`
	AccessToken string `json:"access_token,omitempty"`
}

// Generate a secret hash for Cognito
func generateSecretHash(username, clientID, clientSecret string) string {
	message := username + clientID
	h := hmac.New(sha256.New, []byte(clientSecret))
	h.Write([]byte(message))
	return base64.StdEncoding.EncodeToString(h.Sum(nil))
}

func confirmSignUpHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Printf("Received request body: %s", request.Body)

	// Unmarshal the request body into the confirmRequest struct
	var confirmRequest ConfirmSignUpRequest
	err := json.Unmarshal([]byte(request.Body), &confirmRequest)
	if err != nil {
		log.Printf("Error unmarshalling request body: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusBadRequest,
			Body:       fmt.Sprintf(`{"message": "Invalid request: %v"}`, err),
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	log.Printf("Parsed request: %+v", confirmRequest)

	// Validate request fields
	if confirmRequest.Email == "" || confirmRequest.ConfirmationCode == "" || confirmRequest.Password == "" || confirmRequest.Username == "" || confirmRequest.PhoneNumber == "" {
		log.Printf("Error: Missing email, confirmation code, password, username, or phone number")
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusBadRequest,
			Body:       `{"message": "Missing email, confirmation code, password, username, or phone number"}`,
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	// Create a new AWS session
	sess, err := session.NewSession(&aws.Config{
		Region: aws.String(region),
	})
	if err != nil {
		log.Printf("Failed to create AWS session: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       fmt.Sprintf(`{"message": "Failed to create AWS session: %v"}`, err),
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	svc := cognitoidentityprovider.New(sess)

	// Confirm the user's sign up using the confirmation code
	secretHash := generateSecretHash(confirmRequest.Email, clientID, clientSecret)
	input := &cognitoidentityprovider.ConfirmSignUpInput{
		ClientId:         aws.String(clientID),
		SecretHash:       aws.String(secretHash),
		Username:         aws.String(confirmRequest.Email),
		ConfirmationCode: aws.String(confirmRequest.ConfirmationCode),
	}

	_, err = svc.ConfirmSignUp(input)
	if err != nil {
		log.Printf("Error confirming sign up: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusBadRequest,
			Body:       fmt.Sprintf(`{"message": "Error confirming sign up: %v"}`, err),
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	// Save the user data to DynamoDB
	dynaSvc := dynamodb.New(sess)
	item := map[string]*dynamodb.AttributeValue{
		"email": {
			S: aws.String(confirmRequest.Email),
		},
		"username": {
			S: aws.String(confirmRequest.Username),
		},
		"password": {
			S: aws.String(confirmRequest.Password),
		},
		"phone_number": {
			S: aws.String(confirmRequest.PhoneNumber),
		},
	}

	inputPut := &dynamodb.PutItemInput{
		TableName: aws.String(usersTableName),
		Item:      item,
	}

	log.Printf("Saving user to DynamoDB: %+v", inputPut)

	_, err = dynaSvc.PutItem(inputPut)
	if err != nil {
		log.Printf("Error putting user into DynamoDB: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       fmt.Sprintf(`{"message": "Error putting user into DynamoDB: %v"}`, err),
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	// Initiate authentication to get the access token
	authInput := &cognitoidentityprovider.InitiateAuthInput{
		AuthFlow: aws.String("USER_PASSWORD_AUTH"),
		AuthParameters: map[string]*string{
			"USERNAME":    aws.String(confirmRequest.Email),
			"PASSWORD":    aws.String(confirmRequest.Password),
			"SECRET_HASH": aws.String(secretHash),
		},
		ClientId: aws.String(clientID),
	}

	authResult, err := svc.InitiateAuth(authInput)
	if err != nil {
		log.Printf("Error initiating auth: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       fmt.Sprintf(`{"message": "Error initiating auth: %v"}`, err),
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	log.Printf("Auth result: %+v", authResult)

	var totpSecret string
	var accessToken string

	if authResult.AuthenticationResult != nil {
		accessToken = *authResult.AuthenticationResult.AccessToken

		// Generate TOTP secret
		associateInput := &cognitoidentityprovider.AssociateSoftwareTokenInput{
			AccessToken: &accessToken,
		}

		associateResult, err := svc.AssociateSoftwareToken(associateInput)
		if err != nil {
			log.Printf("Error associating software token: %v", err)
			return events.APIGatewayProxyResponse{
				StatusCode: http.StatusInternalServerError,
				Body:       fmt.Sprintf(`{"message": "Error associating software token: %v"}`, err),
				Headers: map[string]string{
					"Access-Control-Allow-Origin":      "*",
					"Access-Control-Allow-Credentials": "true",
				},
			}, nil
		}

		if associateResult == nil || associateResult.SecretCode == nil {
			log.Printf("Failed to associate software token or retrieve SecretCode")
			return events.APIGatewayProxyResponse{
				StatusCode: http.StatusInternalServerError,
				Body:       `{"message": "Failed to associate software token or retrieve SecretCode"}`,
				Headers: map[string]string{
					"Access-Control-Allow-Origin":      "*",
					"Access-Control-Allow-Credentials": "true",
				},
			}, nil
		}

		totpSecret = *associateResult.SecretCode

		// Enable MFA for the user
		setMfaInput := &cognitoidentityprovider.AdminSetUserMFAPreferenceInput{
			Username:   aws.String(confirmRequest.Email),
			UserPoolId: aws.String(userPoolID),
			SMSMfaSettings: &cognitoidentityprovider.SMSMfaSettingsType{
				Enabled:     aws.Bool(false),
				PreferredMfa: aws.Bool(false),
			},
			SoftwareTokenMfaSettings: &cognitoidentityprovider.SoftwareTokenMfaSettingsType{
				Enabled:     aws.Bool(true),
				PreferredMfa: aws.Bool(true),
			},
		}

		_, err = svc.AdminSetUserMFAPreference(setMfaInput)
		if err != nil {
			log.Printf("Error setting MFA preference: %v", err)
			return events.APIGatewayProxyResponse{
				StatusCode: http.StatusInternalServerError,
				Body:       fmt.Sprintf(`{"message": "Error setting MFA preference: %v"}`, err),
				Headers: map[string]string{
					"Access-Control-Allow-Origin":      "*",
					"Access-Control-Allow-Credentials": "true",
				},
			}, nil
		}
	}

	response := ConfirmSignUpResponse{
		Message:    "User confirmed successfully",
		TOTPSecret: totpSecret,
		AccessToken: accessToken,
	}

	responseBody, err := json.Marshal(response)
	if err != nil {
		log.Printf("Error marshalling response: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       fmt.Sprintf(`{"message": "Error marshalling response: %v"}`, err),
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Body:       string(responseBody),
		Headers: map[string]string{
			"Access-Control-Allow-Origin":      "*",
			"Access-Control-Allow-Credentials": "true",
		},
	}, nil
}

func main() {
	lambda.Start(confirmSignUpHandler)
}
