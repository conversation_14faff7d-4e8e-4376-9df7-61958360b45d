//CHECKOUT PRODUCTS FUNCTION

data "archive_file" "lambda_code_checkout" {
  type        = "zip"
  source_dir  = "../../../lambda/dev/CheckoutProducts"
  output_path = "./dev-checkout-products.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_iam_role" "lambda_execution_role_checkout" {
  name = "dev-lambda_execution_role_checkout_products_function"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Sid    = ""
      Principal = {
        Service = "lambda.amazonaws.com"
      }
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_execution_policy_checkout" {
  name = "dev_lambda_execution_policy_checkout_products_function"
  role = aws_iam_role.lambda_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObjectAcl",
          "s3:GetObject",
          "s3:DeleteObject"
        ]
        Resource = [
          "arn:aws:s3:::${var.s3_bucket}/*",
          "arn:aws:s3:::${var.s3_bucket}/*"
        ]

      },
      {
        Effect = "Allow"
        Action = [
          "execute-api:Invoke"
        ]
        Resource = "arn:aws:execute-api:*:*:*"
      }
    ]
  })
}

//CHECKOUT PRODUCTS FUNCTION
resource "aws_s3_object" "lambda_code_checkout" {
  bucket       = var.s3_bucket
  key          = "dev-checkout-products.zip"
  source       = data.archive_file.lambda_code_checkout.output_path
  etag         = filemd5(data.archive_file.lambda_code_checkout.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "checkout_products_function" {
  function_name = var.checkout_products_function
  s3_bucket     = aws_s3_object.lambda_code_checkout.bucket
  s3_key        = aws_s3_object.lambda_code_checkout.key
  role          = aws_iam_role.lambda_execution_role.arn
  handler       = "dev-checkout-products"
  runtime       = "provided.al2"
  # filename = "../lambdafunction/package/list-products.zip"
  source_code_hash = data.archive_file.lambda_code_checkout.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"         = var.s3_bucket
      "s3_bucket_NAME"      = var.s3_bucket
      STRIPE_SECRET_KEY     = var.stripe_secret_key
      FRONTEND_URL          = var.frontend_url
      DYNAMODB_ORDERS_TABLE = var.dynamodb_orders_table
    }
  }
}

# Lambda permissions are handled by the API Gateway module to avoid circular dependencies

