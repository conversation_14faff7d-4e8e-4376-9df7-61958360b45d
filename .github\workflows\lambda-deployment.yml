name: Lambda Functions Deployment

on:
  push:
    branches: [dev, development]
    paths: ['lambda/**']
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'development'
        type: choice
        options:
          - development
          - production
      functions:
        description: 'Specific functions to deploy (comma-separated, leave empty for all)'
        required: false
        type: string

env:
  AWS_REGION: us-east-1
  GO_VERSION: '1.21'

jobs:
  # Detect changed Lambda functions
  detect-changes:
    name: Detect Changed Functions
    runs-on: ubuntu-latest
    outputs:
      functions: ${{ steps.changes.outputs.functions }}
      deploy_all: ${{ steps.changes.outputs.deploy_all }}
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Detect changed functions
        id: changes
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            if [ -n "${{ inputs.functions }}" ]; then
              # Deploy specific functions
              echo "functions=${{ inputs.functions }}" >> $GITHUB_OUTPUT
              echo "deploy_all=false" >> $GITHUB_OUTPUT
            else
              # Deploy all functions
              echo "deploy_all=true" >> $GITHUB_OUTPUT
            fi
          else
            # Detect changed functions from git diff
            changed_functions=$(git diff --name-only HEAD~1 HEAD | grep '^lambda/' | cut -d'/' -f2 | sort -u | tr '\n' ',' | sed 's/,$//')
            if [ -n "$changed_functions" ]; then
              echo "functions=$changed_functions" >> $GITHUB_OUTPUT
              echo "deploy_all=false" >> $GITHUB_OUTPUT
              echo "Changed functions: $changed_functions"
            else
              echo "No Lambda functions changed"
              echo "deploy_all=false" >> $GITHUB_OUTPUT
              echo "functions=" >> $GITHUB_OUTPUT
            fi
          fi

  # Build and deploy Lambda functions
  deploy-functions:
    name: Deploy Lambda Functions
    runs-on: ubuntu-latest
    needs: [detect-changes]
    if: needs.detect-changes.outputs.deploy_all == 'true' || needs.detect-changes.outputs.functions != ''
    environment: ${{ inputs.environment || 'development' }}
    
    strategy:
      matrix:
        function: ${{ fromJson(needs.detect-changes.outputs.deploy_all == 'true' && '["adminAddProduct", "adminDeleteProduct", "adminGetProductById", "adminImageResizer", "adminListProducts", "adminPreSignUrl", "adminPutProduct", "authConfirmSignup", "authCreateAuthChallenge", "authDefineAuthChallenge", "authForgotPassword", "authLogin", "authLogout", "authPostAuthChallenge", "authPostConfirmation", "authPreAuthentication", "authPreSignup", "authRefreshToken", "authResendCode", "authSignup", "authTtopSecret", "authVerifyAuthChallengeResponse", "authVerifyMFA", "checkoutProducts", "contactEmail", "listProducts", "markSoldProducts", "sesNotification", "tokenAuthorizer", "webhooks"]' || format('[{0}]', join(split(needs.detect-changes.outputs.functions, ','), '", "'))) }}
      fail-fast: false
      max-parallel: 5
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Build Lambda function
        run: |
          FUNCTION_NAME="${{ matrix.function }}"
          FUNCTION_DIR="lambda/$FUNCTION_NAME"
          
          if [ ! -d "$FUNCTION_DIR" ]; then
            echo "Function directory $FUNCTION_DIR does not exist"
            exit 1
          fi
          
          echo "Building $FUNCTION_NAME..."
          cd "$FUNCTION_DIR"
          
          # Initialize go module if it doesn't exist
          if [ ! -f "go.mod" ]; then
            echo "Initializing go module for $FUNCTION_NAME"
            go mod init "$FUNCTION_NAME"
            
            # Add common Lambda dependencies
            go get github.com/aws/aws-lambda-go/lambda
            go get github.com/aws/aws-lambda-go/events
            go get github.com/aws/aws-sdk-go/aws
            go get github.com/aws/aws-sdk-go/service/dynamodb
            go get github.com/aws/aws-sdk-go/service/s3
            go get github.com/aws/aws-sdk-go/service/ses
            go get github.com/aws/aws-sdk-go/service/cognitoidentityprovider
          fi
          
          # Download dependencies
          go mod tidy
          
          # Build the function
          GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o bootstrap *.go
          
          # Create deployment package
          zip "${FUNCTION_NAME}.zip" bootstrap
          
          echo "✅ Built $FUNCTION_NAME successfully"

      - name: Deploy to AWS Lambda
        run: |
          FUNCTION_NAME="${{ matrix.function }}"
          FUNCTION_DIR="lambda/$FUNCTION_NAME"
          ENVIRONMENT="${{ inputs.environment || 'development' }}"
          
          cd "$FUNCTION_DIR"
          
          # Determine the actual Lambda function name based on environment
          if [ "$ENVIRONMENT" = "development" ]; then
            AWS_FUNCTION_NAME="dev-$FUNCTION_NAME"
          else
            AWS_FUNCTION_NAME="$FUNCTION_NAME"
          fi
          
          echo "Deploying $FUNCTION_NAME to $AWS_FUNCTION_NAME..."
          
          # Check if function exists
          if aws lambda get-function --function-name "$AWS_FUNCTION_NAME" >/dev/null 2>&1; then
            echo "Updating existing function: $AWS_FUNCTION_NAME"
            aws lambda update-function-code \
              --function-name "$AWS_FUNCTION_NAME" \
              --zip-file "fileb://${FUNCTION_NAME}.zip"
            
            # Wait for update to complete
            aws lambda wait function-updated --function-name "$AWS_FUNCTION_NAME"
            
            echo "✅ Updated $AWS_FUNCTION_NAME successfully"
          else
            echo "⚠️  Function $AWS_FUNCTION_NAME does not exist. It may need to be created via Terraform first."
            echo "Uploading package to S3 for Terraform to use..."
            
            # Upload to S3 bucket for Terraform to pick up
            S3_BUCKET="dev-thealpinestudio-lambda-functions-v1"
            aws s3 cp "${FUNCTION_NAME}.zip" "s3://$S3_BUCKET/" || echo "Failed to upload to S3"
          fi

      - name: Test function deployment
        run: |
          FUNCTION_NAME="${{ matrix.function }}"
          ENVIRONMENT="${{ inputs.environment || 'development' }}"
          
          if [ "$ENVIRONMENT" = "development" ]; then
            AWS_FUNCTION_NAME="dev-$FUNCTION_NAME"
          else
            AWS_FUNCTION_NAME="$FUNCTION_NAME"
          fi
          
          echo "Testing $AWS_FUNCTION_NAME..."
          
          # Simple test to see if function is invokable
          if aws lambda invoke --function-name "$AWS_FUNCTION_NAME" --payload '{}' /tmp/response.json >/dev/null 2>&1; then
            echo "✅ Function $AWS_FUNCTION_NAME is invokable"
          else
            echo "⚠️  Function $AWS_FUNCTION_NAME may not be ready or doesn't exist yet"
          fi

  # Deployment summary
  deployment-summary:
    name: Deployment Summary
    runs-on: ubuntu-latest
    needs: [detect-changes, deploy-functions]
    if: always()
    
    steps:
      - name: Generate deployment summary
        run: |
          echo "## Lambda Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "**Environment:** ${{ inputs.environment || 'development' }}" >> $GITHUB_STEP_SUMMARY
          echo "**Trigger:** ${{ github.event_name }}" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ needs.detect-changes.outputs.deploy_all }}" = "true" ]; then
            echo "**Scope:** All functions" >> $GITHUB_STEP_SUMMARY
          elif [ -n "${{ needs.detect-changes.outputs.functions }}" ]; then
            echo "**Functions:** ${{ needs.detect-changes.outputs.functions }}" >> $GITHUB_STEP_SUMMARY
          else
            echo "**Result:** No functions to deploy" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "**Status:** ${{ needs.deploy-functions.result == 'success' && '✅ Success' || needs.deploy-functions.result == 'skipped' && '⏭️ Skipped' || '❌ Failed' }}" >> $GITHUB_STEP_SUMMARY
