
  

  {
    "version": "1",
    "triggerSource": "PreSignUp_SignUp",
    "region": "us-west-2",
    "userPoolId": "us-west-2_GOD2mBQiC",
    "userName": "testuser",
    "callerContext": {
      "awsSdkVersion": "aws-sdk-unknown-unknown",
      "clientId": "6ej9p4miap6snoncgrj3kjtme"
    },
    "request": {
      "userAttributes": {
        "email": "<EMAIL>",
        "phone_number": "+17325985266",
        "name": "Test User",
        "password": "UserProvidedPassword123!"
      },
      "validationData": {}
    },
    "response": {
      "autoConfirmUser": false,
      "autoVerifyEmail": false,
      "autoVerifyPhone": false
    }
  }
  




  {
    "ClientId": "1harpikirsevqpo08q1g5jff8d",
    "ClientSecret": "st0isd0oe2cftv53e4uo5iqg2o3q33kgu55sllj8o2jdgjmqa8b",
    "body": "{\"email\": \"<EMAIL>\", \"password\": \"UserProvidedPassword123!\", \"name\": \"Ttjprohammer\", \"phone_number\": \"+17325985266\"}"
  }
  