# TheAlpineStudio
Greenpassion1
Ariel Art Website  
  
# ToDos  
- filter certain categories of animals ( birds, mamals,fish, etc? )  
- add billing to stripe lambda function 
- display payment confirmation after completed checkout session  
- display "Email Send" and clear text fields for contact page  


aws s3 cp ./build/ s3://thealpinestudio.com/ --recursive

aws s3 cp ./build/ s3://test.thealpinestudio.com-v1/ --recursive

aws s3 cp ./build/ s3://admin.thealpinestudio.com/ --recursive


aws s3 cp ./build/admin.html s3://admin.thealpinestudio.com/admin.html

aws s3 cp ./build/ s3://admin.thealpinestudio.com/ --recursive       
aws cloudfront create-invalidation --distribution-id EOVACGUPC8PZ8 --paths "/*"

//admin   EOVACGUPC8PZ8
aws cloudfront create-invalidation --distribution-id EOVACGUPC8PZ8 --paths "/*"

//Test
aws cloudfront create-invalidation --distribution-id E333R3CHKXLYGZ --paths "/*"

  //prod
  aws cloudfront create-invalidation --distribution-id ETN10ORXOMMDL --paths "/*"
  
secrets setup
need to deploy terraform
upload each lambda
test


gh pr create --base main --head admin-panel --title "site-updated" --body "Site patched and updated. Building Admin Page with MFA and Authentication with Cognito"

gh pr create: This is the command to create a new pull request.
--base main: Specifies the base branch that you want to merge your changes into. In this case, it's the main branch.
--head admin-panel: Indicates the branch that contains your changes. Here, it's named admin-panel.
--title "site-updated": Sets the title of the pull request. It should be brief and descriptive.
--body "Site patched and updated. Building Admin Page with MFA and Authentication with Cognito": Provides a detailed description of the changes made in the pull request. This is useful for reviewers to understand the purpose and impact of the changes.

export PATH="/opt/homebrew/bin/gh:$PATH"

9D78-9CFF


cognito command:  


aws cognito-idp describe-user-pool-client --user-pool-id us-west-2_GOD2mBQiC --client-id <5124353125>

aws cognito-idp list-user-pool-clients --user-pool-id us-west-2_GOD2mBQiC




gh pr create --base main --head admin-panel --title "site-updated" --body "Site patched and updated. Building Admin Page with MFA and Authentication with Cognito"

gh pr create: This is the command to create a new pull request.
--base main: Specifies the base branch that you want to merge your changes into. In this case, it's the main branch.
--head admin-panel: Indicates the branch that contains your changes. Here, it's named admin-panel.
--title "site-updated": Sets the title of the pull request. It should be brief and descriptive.
--body "Site patched and updated. Building Admin Page with MFA and Authentication with Cognito": Provides a detailed description of the changes made in the pull request. This is useful for reviewers to understand the purpose and impact of the changes.

export PATH="/opt/homebrew/bin/gh:$PATH"

9D78-9CFF