//CONTACT PRODUCTS FUNCTION
data "archive_file" "lambda_code_contact" {
  type        = "zip"
  source_dir  = "../../../lambda/testContactEmail"
  output_path = "./prod-contact-email.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "lambda_code_contact" {
  bucket       = var.s3_bucket
  key          = "contact-email.zip"
  source       = data.archive_file.lambda_code_contact.output_path
  etag         = filemd5(data.archive_file.lambda_code_contact.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "contact_email_function" {
  function_name = var.contact_email_function
  s3_bucket     = aws_s3_object.lambda_code_contact.bucket
  s3_key        = aws_s3_object.lambda_code_contact.key
  role          = aws_iam_role.lambda_execution_role.arn
  handler       = "prod-contact-email"
  runtime          = "provided.al2"
  # filename = "../lambdafunction/package/contact-email.zip"
  source_code_hash = data.archive_file.lambda_code_contact.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME" = var.s3_bucket
      "s3_bucket_NAME" = var.s3_bucket
      "STRIPE_SECRET_KEY" = var.stripe_secret_key
    }
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_GET_contact" {
  statement_id  = "AllowExecutionFromAPIGatewayGET"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.contact_email_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.api_gateway_region}:${var.api_gateway_account_id}:${var.api_gateway_id}/*/GET/contact-email"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_POST_contact" {
  statement_id  = "AllowExecutionFromAPIGatewayPOST"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.contact_email_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.api_gateway_region}:${var.api_gateway_account_id}:${var.api_gateway_id}/*/POST/contact-email"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_contact" {
  statement_id  = "AllowExecutionFromAPIGatewayV1"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.contact_email_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "${var.api_gateway_arn}/*/*"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}
