//Sold Product Lambda Function
//----------------------------------------------------------------
data "archive_file" "sold_product_lambda_archive" {
  type        = "zip"
  source_dir  = "../../../lambda/markSoldProducts/"
  output_path = "./sold-product.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "sold_product_lambda_s3_object" {
  bucket       = var.s3_bucket
  key          = "sold-product.zip"
  source       = data.archive_file.sold_product_lambda_archive.output_path
  etag         = filemd5(data.archive_file.sold_product_lambda_archive.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "sold_product_lambda_functions" {
  function_name    = var.auth_cognito_sold_product_function_name
  s3_bucket        = aws_s3_object.sold_product_lambda_s3_object.bucket
  s3_key           = aws_s3_object.sold_product_lambda_s3_object.key
  role             = aws_iam_role.lambda_execution_role_auth_cognito.arn
  handler          = "sold-product.lambda_handler"
  runtime          = "provided.al2"
  source_code_hash = data.archive_file.sold_product_lambda_archive.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME" = var.s3_bucket
      TABLE_NAME    = var.dynamo_table_products_name
    }
  }
}

resource "aws_lambda_permission" "sold_product_lambda_permissions" {
  statement_id  = "AllowAPIGatewayInvoke-${var.auth_cognito_sold_product_function_name}"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.sold_product_lambda_functions.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.aws_region}:${var.api_gateway_account_id}:${var.admin_api_gateway_id}/*/POST/sold-product"

  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}


resource "aws_iam_policy" "lambda_dynamodb_policy" {
  name   = "LambdaDynamoDBUpdatePolicy"
  path   = "/"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "dynamodb:UpdateItem"
        ]
        Effect   = "Allow"
        Resource = "arn:aws:dynamodb:${var.aws_region}:${var.api_gateway_account_id}:table/${var.dynamo_table_products_name}"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "lambda_dynamodb_policy_attachment" {
  role       = aws_iam_role.lambda_execution_role_auth_cognito.name
  policy_arn = aws_iam_policy.lambda_dynamodb_policy.arn
}
