#!/bin/bash

# Shell script to deploy a single development Lambda function
# Usage: ./deploy-single-lambda.sh dev-list-products
# ========================================================

# Check if function name is provided
if [ $# -eq 0 ]; then
    echo "Usage: $0 <function-name>"
    echo "Available functions: dev-list-products, dev-checkout-products, dev-contact-email, dev-webhooks"
    exit 1
fi

FUNCTION_NAME=$1

# Configuration
REGION="us-west-2"
PROFILE="thealpinestudio"
BUCKET_NAME="dev-thealpinestudio-lambda-functions-v1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function configuration
case $FUNCTION_NAME in
    "dev-list-products")
        DIRECTORY="lambda/dev/ListProducts"
        SOURCE_FILE="listProducts.go"
        ZIP_FILE="list-products.zip"
        S3_KEY="list-products.zip"
        TEST_EVENT='{"httpMethod":"GET","queryStringParameters":{}}'
        ;;
    "dev-checkout-products")
        DIRECTORY="lambda/dev/CheckoutProducts"
        SOURCE_FILE="checkoutProducts.go"
        ZIP_FILE="checkout-products.zip"
        S3_KEY="checkout-products.zip"
        TEST_EVENT='{"httpMethod":"POST","body":"{\"items\":[{\"id\":\"1743209634\",\"title\":\"All Eyes on You Sticker\",\"quantity\":1,\"size\":\"Sticker\",\"price\":6}],\"customerInfo\":{\"email\":\"<EMAIL>\"}}"}'
        ;;
    "dev-contact-email")
        DIRECTORY="lambda/dev/ContactEmail"
        SOURCE_FILE="contactEmail.go"
        ZIP_FILE="contact-email.zip"
        S3_KEY="contact-email.zip"
        TEST_EVENT='{"httpMethod":"POST","body":"{\"name\":\"Test User\",\"email\":\"<EMAIL>\",\"message\":\"This is a test message\"}"}'
        ;;
    "dev-webhooks")
        DIRECTORY="lambda/dev/Webhooks"
        SOURCE_FILE="emailWebhook.go"
        ZIP_FILE="webhooks.zip"
        S3_KEY="webhooks.zip"
        TEST_EVENT='{"httpMethod":"POST","body":"{\"type\":\"checkout.session.completed\",\"data\":{\"object\":{\"id\":\"cs_test_123\"}}}"}'
        ;;
    *)
        echo -e "${RED}Error: Unknown function name '$FUNCTION_NAME'${NC}"
        echo "Available functions: dev-list-products, dev-checkout-products, dev-contact-email, dev-webhooks"
        exit 1
        ;;
esac

echo -e "${CYAN}=== Deploying $FUNCTION_NAME ===${NC}"

# Navigate to the function directory
pushd "$DIRECTORY" > /dev/null

# Set environment variables for Go build
export GOOS=linux
export GOARCH=amd64
export CGO_ENABLED=0

# Build the Lambda function
echo -e "${YELLOW}Building Lambda function...${NC}"
go build -o bootstrap "$SOURCE_FILE"
if [ $? -ne 0 ]; then
    echo -e "${RED}Build failed.${NC}"
    popd > /dev/null
    exit 1
else
    echo -e "${GREEN}Build succeeded.${NC}"
fi

# Zip the executable
echo -e "${YELLOW}Creating zip file...${NC}"
zip "$ZIP_FILE" bootstrap
if [ $? -ne 0 ]; then
    echo -e "${RED}Zip failed.${NC}"
    popd > /dev/null
    exit 1
else
    echo -e "${GREEN}Zip succeeded.${NC}"
fi

# Upload the zipped file to S3
echo -e "${YELLOW}Uploading to S3...${NC}"
aws s3 cp "$ZIP_FILE" "s3://$BUCKET_NAME/$S3_KEY" --region "$REGION" --profile "$PROFILE"
if [ $? -ne 0 ]; then
    echo -e "${RED}S3 upload failed.${NC}"
    popd > /dev/null
    exit 1
else
    echo -e "${GREEN}S3 upload succeeded.${NC}"
fi

# Update the Lambda function with the code from S3
echo -e "${YELLOW}Updating Lambda function...${NC}"
aws lambda update-function-code \
    --function-name "$FUNCTION_NAME" \
    --s3-bucket "$BUCKET_NAME" \
    --s3-key "$S3_KEY" \
    --region "$REGION" \
    --profile "$PROFILE"
if [ $? -ne 0 ]; then
    echo -e "${RED}Lambda function update failed.${NC}"
    popd > /dev/null
    exit 1
else
    echo -e "${GREEN}Lambda function update succeeded.${NC}"
fi

# Return to the original directory
popd > /dev/null

# Test the function
echo -e "${CYAN}=== Testing $FUNCTION_NAME ===${NC}"

# Create a temporary test event file
TEST_EVENT_FILE="test-event-$FUNCTION_NAME.json"
echo "$TEST_EVENT" > "$TEST_EVENT_FILE"

# Wait a moment for the Lambda function to be ready
sleep 5

# Invoke the Lambda function
echo -e "${YELLOW}Invoking Lambda function...${NC}"
RESPONSE_FILE="response-$FUNCTION_NAME.json"
aws lambda invoke \
    --function-name "$FUNCTION_NAME" \
    --cli-binary-format raw-in-base64-out \
    --payload "file://$TEST_EVENT_FILE" \
    --region "$REGION" \
    --profile "$PROFILE" \
    "$RESPONSE_FILE"

# Check if the invocation was successful
if [ $? -ne 0 ]; then
    echo -e "${RED}Lambda function invocation failed.${NC}"
    exit 1
else
    echo -e "${GREEN}Lambda function invocation succeeded.${NC}"

    # Display the response
    echo -e "${YELLOW}Response:${NC}"
    cat "$RESPONSE_FILE"
    echo ""

    # Check if the response contains an error
    if grep -q "FunctionError" "$RESPONSE_FILE"; then
        echo -e "${RED}Lambda function returned an error.${NC}"
        exit 1
    fi
fi

# Clean up temporary files
rm -f "$TEST_EVENT_FILE" "$RESPONSE_FILE"

echo -e "\n${GREEN}=== Deployment and Test Complete ===${NC}"
