#!/bin/bash

# Run build
echo "Building client..."
cd ../client/
npm run build
if [ $? -ne 0 ]; then
    echo "Build failed"
    exit 1
fi

# Copy build folder to s3 bucket
echo "Copying build folder to s3 bucket..."
cd ../staging/
bucket_name=$(terraform output -raw bucket_name)
aws s3 cp ../client/build/ s3://${bucket_name}/ --recursive
if [ $? -ne 0 ]; then
    echo "S3 copy failed"
    exit 1
fi

# Create cloudfront invalidation
echo "Creating cloudfront invalidation..."
distribution_id="E2YD294PU9QJUN" # Replace this with your distribution ID
aws cloudfront create-invalidation --distribution-id ${distribution_id} --paths "/*"
if [ $? -ne 0 ]; then
    echo "Cloudfront invalidation failed"
    exit 1
fi

echo "Operation completed successfully"

