package main

import (
    "context"
    "fmt"
    "log"
    "os"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/dynamodb"
)

var (
    tableName = os.Getenv("TOTP_TABLE_NAME")
    region    = os.Getenv("AWS_REGION")
)

func handlePreAuthentication(ctx context.Context, event events.CognitoEventUserPoolsPreAuthentication) (events.CognitoEventUserPoolsPreAuthentication, error) {
    log.Println("Pre-authentication event:", event)

    email := event.Request.UserAttributes["email"]
    if email == "" {
        errMsg := "user email not found in request"
        log.Println(errMsg)
        return event, fmt.Errorf(errMsg)
    }

    log.Printf("Checking if user with email %s is allowed to sign in", email)

    sess, err := session.NewSession(&aws.Config{
        Region: aws.String(region),
    })
    if err != nil {
        log.Printf("Failed to create AWS session: %v", err)
        return event, err
    }

    allowed, err := isUserAllowed(sess, email)
    if err != nil {
        log.Printf("Failed to check user in DynamoDB: %v", err)
        return event, err
    }

    if !allowed {
        errMsg := fmt.Sprintf("User with email %s is not allowed to sign in", email)
        log.Println(errMsg)
        return event, fmt.Errorf(errMsg)
    }

    return event, nil
}

func isUserAllowed(sess *session.Session, email string) (bool, error) {
    svc := dynamodb.New(sess)

    input := &dynamodb.GetItemInput{
        TableName: aws.String(tableName),
        Key: map[string]*dynamodb.AttributeValue{
            "email": {
                S: aws.String(email),
            },
        },
    }

    log.Printf("DynamoDB GetItem input: %+v", input)

    result, err := svc.GetItem(input)
    if err != nil {
        log.Printf("Error getting item from DynamoDB: %v", err)
        return false, fmt.Errorf("failed to get item from DynamoDB: %v", err)
    }

    log.Printf("DynamoDB GetItem result: %+v", result)

    if result.Item == nil {
        log.Println("No item found for user")
        return false, nil
    }

    log.Println("Item found for user")
    return true, nil
}

func main() {
    lambda.Start(handlePreAuthentication)
}
