# Instructions for setting up AWS Secrets Manager secrets for The Alpine Studio
#
# IMPORTANT: Do NOT run this script!
# Terraform will create the secret containers automatically.
# You only need to populate the secret values manually.

param(
    [string]$Region = "us-west-2",
    [string]$Profile = "thealpinestudio"
)

Write-Host "🔐 AWS Secrets Manager Setup Instructions" -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Green
Write-Host ""
Write-Host "⚠️  IMPORTANT: This script provides instructions only!" -ForegroundColor Red
Write-Host "⚠️  Do NOT run the commands below automatically!" -ForegroundColor Red
Write-Host "⚠️  Terraform will create the secret containers." -ForegroundColor Red
Write-Host ""

Write-Host "STEP 1: Deploy infrastructure first" -ForegroundColor Yellow
Write-Host "This will create the secret containers via Terraform:" -ForegroundColor White
Write-Host "- dev/stripe/publishable-key" -ForegroundColor Gray
Write-Host "- dev/stripe/secret-key" -ForegroundColor Gray
Write-Host "- dev/stripe/endpoint-secret" -ForegroundColor Gray
Write-Host "- dev/resend/api-key" -ForegroundColor Gray
Write-Host "- dev/frontend/api-routes (auto-populated)" -ForegroundColor Gray
Write-Host ""

Write-Host "STEP 2: Manually populate secret values" -ForegroundColor Yellow
Write-Host "After Terraform creates the containers, run these commands:" -ForegroundColor White
Write-Host ""

Write-Host "# Development secrets:" -ForegroundColor Cyan
Write-Host "aws secretsmanager put-secret-value --secret-id 'dev/stripe/publishable-key' --secret-string 'pk_test_YOUR_KEY' --region $Region --profile $Profile" -ForegroundColor Gray
Write-Host "aws secretsmanager put-secret-value --secret-id 'dev/stripe/secret-key' --secret-string 'sk_test_YOUR_KEY' --region $Region --profile $Profile" -ForegroundColor Gray
Write-Host "aws secretsmanager put-secret-value --secret-id 'dev/stripe/endpoint-secret' --secret-string 'whsec_YOUR_SECRET' --region $Region --profile $Profile" -ForegroundColor Gray
Write-Host "aws secretsmanager put-secret-value --secret-id 'dev/resend/api-key' --secret-string 'YOUR_RESEND_KEY' --region $Region --profile $Profile" -ForegroundColor Gray
Write-Host ""

Write-Host "# Production secrets:" -ForegroundColor Cyan
Write-Host "aws secretsmanager put-secret-value --secret-id 'prod/stripe/publishable-key' --secret-string 'pk_live_YOUR_KEY' --region $Region --profile $Profile" -ForegroundColor Gray
Write-Host "aws secretsmanager put-secret-value --secret-id 'prod/stripe/secret-key' --secret-string 'sk_live_YOUR_KEY' --region $Region --profile $Profile" -ForegroundColor Gray
Write-Host "aws secretsmanager put-secret-value --secret-id 'prod/stripe/endpoint-secret' --secret-string 'whsec_YOUR_SECRET' --region $Region --profile $Profile" -ForegroundColor Gray
Write-Host "aws secretsmanager put-secret-value --secret-id 'prod/resend/api-key' --secret-string 'YOUR_RESEND_KEY' --region $Region --profile $Profile" -ForegroundColor Gray
Write-Host ""

Write-Host "STEP 3: Alternative - Use AWS Console" -ForegroundColor Yellow
Write-Host "You can also set secret values in the AWS Console:" -ForegroundColor White
Write-Host "1. Go to AWS Secrets Manager in the console" -ForegroundColor Gray
Write-Host "2. Find the secrets created by Terraform" -ForegroundColor Gray
Write-Host "3. Click 'Retrieve secret value' -> 'Edit'" -ForegroundColor Gray
Write-Host "4. Enter your secret values" -ForegroundColor Gray
Write-Host ""

Write-Host "WORKFLOW:" -ForegroundColor Green
Write-Host "1. 🏗️  Run infrastructure deployment (creates secret containers)" -ForegroundColor White
Write-Host "2. 🔑 Populate secret values (using commands above or AWS Console)" -ForegroundColor White
Write-Host "3. 🚀 Run frontend deployment (reads secrets automatically)" -ForegroundColor White
Write-Host "4. 🗑️  Remove GitHub secrets (optional, after testing)" -ForegroundColor White


# Add your actual secret values here
aws secretsmanager put-secret-value --secret-id "dev/stripe/publishable-key" --secret-string "pk_test_51NirT5H1KC3YYzQbs2HrEGrXz7TDCVHJBGGPQ9o0TGAraTO2OjZIDEvdA8gpiDwzJU1CtPsAGGvgwI0GPC7MYGsJ00GOiSzTRw" --region us-west-2 --profile thealpinestudio
aws secretsmanager put-secret-value --secret-id "dev/stripe/secret-key" --secret-string "sk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL" --region us-west-2 --profile thealpinestudio
aws secretsmanager put-secret-value --secret-id "dev/stripe/endpoint-secret" --secret-string "whsec_Kh2KKZZnTceUvS6YCngbv75A2YatEgzy" --region us-west-2 --profile thealpinestudio
aws secretsmanager put-secret-value --secret-id "dev/resend/api-key" --secret-string "re_ad1veToT_2yFABNEpT9F3dqUMy73Q6P3t" --region us-west-2 --profile thealpinestudio