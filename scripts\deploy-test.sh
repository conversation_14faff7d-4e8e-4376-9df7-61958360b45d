#!/bin/bash

# Quick deployment script for test environment
# This script builds and deploys the frontend to test.thealpinestudio.com
# Note: Development environment deploys to test.thealpinestudio.com

echo "=== Deploying to Test Environment ==="

# Build for development environment (which deploys to test.thealpinestudio.com)
./scripts/build-frontend.sh development

echo "=== Test deployment completed! ==="
echo "Visit: https://test.thealpinestudio.com"
