# CI/CD Workflows for The Alpine Studio

This directory contains GitHub Actions workflows for continuous integration and deployment of The Alpine Studio project.

## Branching Strategy

We use the following branches for different purposes:

- `dev` or `development`: Development branch for ongoing work
- `feature/*`: Feature branches for new features
- `main` or `master`: Production branch for live site

## Workflows

### Development Environment Deployment (`dev-deployment.yml`)

This workflow deploys to the development environment.

**Triggers:**
- Push to `dev` or `development` branch
- Push to `feature/*` branches (but doesn't deploy automatically)
- Pull requests to `dev` or `develop` branches (but doesn't deploy automatically)
- Manual trigger via GitHub Actions UI

**What it does:**
1. Builds and deploys Lambda functions
2. Applies Terraform configuration for development environment
3. Builds and deploys the frontend to the test CloudFront distribution
4. Creates a tag for the deployment

### Production Environment Deployment (`prod-deployment.yml`)

This workflow deploys to the production environment.

**Triggers:**
- Push to `main` or `master` branch
- Manual trigger via GitHub Actions UI

**What it does:**
1. Builds and deploys Lambda functions
2. Applies Terraform configuration for production environment
3. Builds and deploys the frontend to the production CloudFront distribution
4. Creates a tag for the deployment

## Tagging

Tags are automatically created for deployments:
- For automatic deployments, tags follow the format: `{branch-name}-{timestamp}`
- For manual deployments, you can specify a custom tag

## Environment Variables

The workflows use environment variables to configure the build and deployment:

### Development Environment:
- API endpoints: `https://api-dev.thealpinestudio.com/dev-*`
- Frontend URL: `https://test.thealpinestudio.com`
- CloudFront distribution: `E333R3CHKXLYGZ`

### Production Environment:
- API endpoints: Production API Gateway endpoints
- Frontend URL: `https://thealpinestudio.com`
- CloudFront distribution: `ETN10ORXOMMDL`

## Manual Deployment

To manually trigger a deployment:
1. Go to the "Actions" tab in GitHub
2. Select the workflow you want to run
3. Click "Run workflow"
4. Select the branch to deploy from
5. Optionally enter a tag name
6. Click "Run workflow"
