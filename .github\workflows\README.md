# CI/CD Workflows for The Alpine Studio

This directory contains GitHub Actions workflows for continuous integration and deployment of The Alpine Studio project.

## Table of Contents

- [Branching Strategy](#branching-strategy)
- [Workflows](#workflows)
- [Pipeline Architecture & Best Practices](#pipeline-architecture--best-practices)
- [Setting Up CI/CD Pipelines](#setting-up-cicd-pipelines)
- [Security & Secrets Management](#security--secrets-management)
- [Performance Optimization](#performance-optimization)
- [Rollback Strategies](#rollback-strategies)
- [Monitoring & Troubleshooting](#monitoring--troubleshooting)

## Branching Strategy

We use the following branches for different purposes:

- `dev` or `development`: Development branch for ongoing work
- `feature/*`: Feature branches for new features
- `main` or `master`: Production branch for live site

## Workflows

### Development Environment Deployment (`dev-deployment.yml`)

This workflow deploys to the development environment.

**Triggers:**

- Push to `dev` or `development` branch
- Push to `feature/*` branches (but doesn't deploy automatically)
- Pull requests to `dev` or `develop` branches (but doesn't deploy automatically)
- Manual trigger via GitHub Actions UI

**What it does:**

1. Builds and deploys Lambda functions
2. Applies Terraform configuration for development environment
3. Builds and deploys the frontend to the test CloudFront distribution
4. Creates a tag for the deployment

### Production Environment Deployment (`prod-deployment.yml`)

This workflow deploys to the production environment.

**Triggers:**

- Push to `main` or `master` branch
- Manual trigger via GitHub Actions UI

**What it does:**

1. Builds and deploys Lambda functions
2. Applies Terraform configuration for production environment
3. Builds and deploys the frontend to the production CloudFront distribution
4. Creates a tag for the deployment

## Tagging

Tags are automatically created for deployments:

- For automatic deployments, tags follow the format: `{branch-name}-{timestamp}`
- For manual deployments, you can specify a custom tag

## Environment Variables

The workflows use environment variables to configure the build and deployment:

### Development Environment:

- API endpoints: `https://api-dev.thealpinestudio.com/dev-*`
- Frontend URL: `https://test.thealpinestudio.com`
- CloudFront distribution: `E333R3CHKXLYGZ`

### Production Environment:

- API endpoints: Production API Gateway endpoints
- Frontend URL: `https://thealpinestudio.com`
- CloudFront distribution: `ETN10ORXOMMDL`

## Manual Deployment

To manually trigger a deployment:

1. Go to the "Actions" tab in GitHub
2. Select the workflow you want to run
3. Click "Run workflow"
4. Select the branch to deploy from
5. Optionally enter a tag name
6. Click "Run workflow"

## Pipeline Architecture & Best Practices

Our CI/CD pipelines follow industry best practices for reliability, security, and performance:

### Modular Pipeline Design

Each pipeline stage is designed to be:

- **Small and focused**: Each job handles a specific task (build, test, deploy)
- **Independent**: Jobs can run in isolation and be debugged separately
- **Reusable**: Common tasks are extracted into reusable actions
- **Fail-fast**: Early stages catch issues before expensive deployment operations

### Pipeline Stages

```mermaid
graph LR
    A[Code Push] --> B[Lint & Validate]
    B --> C[Build & Test]
    C --> D[Security Scan]
    D --> E[Deploy Infrastructure]
    E --> F[Deploy Application]
    F --> G[Integration Tests]
    G --> H[Tag Release]
```

### Job Dependencies

- **Parallel execution**: Independent jobs run simultaneously to reduce total pipeline time
- **Sequential gates**: Critical validations must pass before deployment
- **Conditional execution**: Some jobs only run on specific branches or conditions

## Setting Up CI/CD Pipelines

### Prerequisites

Before setting up the pipelines, ensure you have:

1. **AWS Credentials**: Configured as GitHub secrets
2. **Terraform State**: S3 bucket for state storage
3. **Domain Configuration**: CloudFront distributions and Route53 records
4. **Environment Variables**: All required secrets and configuration

### Required GitHub Secrets

Add these secrets to your GitHub repository settings:

```bash
# AWS Configuration
AWS_ACCESS_KEY_ID          # AWS access key for deployments
AWS_SECRET_ACCESS_KEY      # AWS secret key for deployments
AWS_REGION                 # Default AWS region (e.g., us-east-1)

# Terraform Configuration
TF_VAR_aws_profile         # AWS profile name (thealpinestudio)
TF_STATE_BUCKET            # S3 bucket for Terraform state

# Application Configuration
REACT_APP_API_BASE_URL_DEV # Development API base URL
REACT_APP_API_BASE_URL_PROD # Production API base URL

# Optional: Notification Configuration
SLACK_WEBHOOK_URL          # For deployment notifications
DISCORD_WEBHOOK_URL        # Alternative notification channel
```

### Environment-Specific Configuration

#### Development Environment

```yaml
environment: development
api_base_url: https://api-dev.thealpinestudio.com
frontend_url: https://test.thealpinestudio.com
cloudfront_distribution: E333R3CHKXLYGZ
terraform_workspace: development
```

#### Production Environment

```yaml
environment: production
api_base_url: https://api.thealpinestudio.com
frontend_url: https://thealpinestudio.com
cloudfront_distribution: ETN10ORXOMMDL
terraform_workspace: production
```

## Security & Secrets Management

### Secure Secret Handling

- **GitHub Secrets**: All sensitive data stored as encrypted repository secrets
- **Least Privilege**: AWS IAM roles with minimal required permissions
- **Secret Rotation**: Regular rotation of access keys and tokens
- **Environment Isolation**: Separate credentials for development and production

### Security Scanning

Our pipelines include automated security checks:

```yaml
# Example security job
security-scan:
  runs-on: ubuntu-latest
  steps:
    - name: Run Snyk Security Scan
      uses: snyk/actions/node@master
      env:
        SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}

    - name: Run Terraform Security Scan
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: "config"
        scan-ref: "./terraform"
```

### Access Control

- **Branch Protection**: Required reviews for production deployments
- **Environment Protection**: Manual approval gates for production
- **Audit Logging**: All deployment activities are logged and tracked

## Performance Optimization

### Caching Strategies

Our pipelines implement multiple caching layers to reduce build times:

#### 1. Dependency Caching

```yaml
- name: Cache Node.js dependencies
  uses: actions/cache@v3
  with:
    path: ~/.npm
    key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
    restore-keys: |
      ${{ runner.os }}-node-

- name: Cache Go modules
  uses: actions/cache@v3
  with:
    path: ~/go/pkg/mod
    key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
    restore-keys: |
      ${{ runner.os }}-go-
```

#### 2. Build Artifact Caching

```yaml
- name: Cache Terraform providers
  uses: actions/cache@v3
  with:
    path: ~/.terraform.d/plugin-cache
    key: ${{ runner.os }}-terraform-${{ hashFiles('**/.terraform.lock.hcl') }}

- name: Cache Lambda build artifacts
  uses: actions/cache@v3
  with:
    path: ./lambda/dist
    key: lambda-${{ hashFiles('lambda/**/*.go') }}
```

### Parallel Execution

Jobs are organized to maximize parallelism:

```yaml
# Jobs that can run in parallel
jobs:
  lint-frontend:
    runs-on: ubuntu-latest
    # Frontend linting

  lint-backend:
    runs-on: ubuntu-latest
    # Backend linting

  test-frontend:
    runs-on: ubuntu-latest
    # Frontend tests

  test-backend:
    runs-on: ubuntu-latest
    # Backend tests

  # Deployment jobs depend on all tests passing
  deploy:
    needs: [lint-frontend, lint-backend, test-frontend, test-backend]
    runs-on: ubuntu-latest
```

## Rollback Strategies

### Automated Rollback Triggers

Our pipelines include automated rollback mechanisms:

```yaml
- name: Health Check
  run: |
    # Wait for deployment to stabilize
    sleep 30

    # Check application health
    response=$(curl -s -o /dev/null -w "%{http_code}" ${{ env.HEALTH_CHECK_URL }})
    if [ $response -ne 200 ]; then
      echo "Health check failed with status: $response"
      exit 1
    fi

- name: Rollback on Failure
  if: failure()
  run: |
    echo "Deployment failed, initiating rollback..."
    # Revert to previous CloudFront distribution
    aws cloudfront create-invalidation --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"
    # Revert Lambda functions to previous version
    aws lambda update-function-code --function-name ${{ env.FUNCTION_NAME }} --s3-bucket ${{ env.BACKUP_BUCKET }} --s3-key ${{ env.PREVIOUS_VERSION }}
```

### Manual Rollback Procedures

#### Quick Rollback Commands

```bash
# Rollback frontend to previous version
aws s3 sync s3://backup-bucket/frontend-v1.2.3/ s3://production-bucket/
aws cloudfront create-invalidation --distribution-id ETN10ORXOMMDL --paths "/*"

# Rollback Lambda function
aws lambda update-function-code \
  --function-name production-api \
  --s3-bucket lambda-deployments \
  --s3-key previous-version.zip

# Rollback Terraform infrastructure
cd terraform/production
terraform workspace select production
git checkout previous-stable-tag
terraform plan
terraform apply
```

#### Rollback Checklist

1. **Identify the issue**: Determine what needs to be rolled back
2. **Check dependencies**: Ensure rollback won't break dependent services
3. **Execute rollback**: Use appropriate rollback method
4. **Verify functionality**: Run health checks and integration tests
5. **Monitor metrics**: Watch for error rates and performance issues
6. **Document incident**: Record what happened and lessons learned

### Blue-Green Deployment Strategy

For zero-downtime deployments and easy rollbacks:

```yaml
blue-green-deploy:
  runs-on: ubuntu-latest
  steps:
    - name: Deploy to Green Environment
      run: |
        # Deploy new version to green environment
        terraform workspace select green
        terraform apply -auto-approve

    - name: Run Integration Tests
      run: |
        # Test green environment
        npm run test:integration -- --env=green

    - name: Switch Traffic
      run: |
        # Switch Route53 to point to green environment
        aws route53 change-resource-record-sets --hosted-zone-id ${{ env.HOSTED_ZONE_ID }} --change-batch file://switch-to-green.json

    - name: Monitor and Rollback if Needed
      run: |
        # Monitor for 5 minutes
        sleep 300
        # Check error rates
        if [ $(aws logs filter-log-events --log-group-name /aws/lambda/api --filter-pattern "ERROR" --start-time $(date -d '5 minutes ago' +%s)000 | jq '.events | length') -gt 10 ]; then
          # Rollback to blue
          aws route53 change-resource-record-sets --hosted-zone-id ${{ env.HOSTED_ZONE_ID }} --change-batch file://switch-to-blue.json
          exit 1
        fi
```

## Monitoring & Troubleshooting

### Pipeline Monitoring

#### Key Metrics to Track

- **Build Success Rate**: Percentage of successful pipeline runs
- **Build Duration**: Time taken for each pipeline stage
- **Deployment Frequency**: How often deployments occur
- **Mean Time to Recovery (MTTR)**: Time to fix failed deployments
- **Change Failure Rate**: Percentage of deployments causing issues

#### Monitoring Tools Integration

```yaml
- name: Send Metrics to CloudWatch
  run: |
    aws cloudwatch put-metric-data \
      --namespace "CI/CD" \
      --metric-data MetricName=BuildDuration,Value=${{ env.BUILD_DURATION }},Unit=Seconds \
      --metric-data MetricName=BuildSuccess,Value=1,Unit=Count

- name: Notify Slack on Failure
  if: failure()
  uses: 8398a7/action-slack@v3
  with:
    status: failure
    webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
    message: |
      🚨 Deployment Failed!
      Branch: ${{ github.ref }}
      Commit: ${{ github.sha }}
      Author: ${{ github.actor }}
```

### Troubleshooting Common Issues

#### Build Failures

1. **Dependency Issues**

   ```bash
   # Clear cache and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

2. **Test Failures**

   ```bash
   # Run tests locally with verbose output
   npm test -- --verbose
   go test -v ./...
   ```

3. **Terraform Issues**
   ```bash
   # Check Terraform state
   terraform state list
   terraform plan -detailed-exitcode
   ```

#### Deployment Failures

1. **AWS Permission Issues**

   - Verify IAM roles and policies
   - Check AWS credentials in GitHub secrets
   - Ensure cross-account access is configured

2. **Resource Conflicts**

   - Check for resource naming conflicts
   - Verify Terraform state consistency
   - Review AWS resource limits

3. **Network Issues**
   - Verify VPC and security group configurations
   - Check DNS resolution
   - Test connectivity between services

### Debugging Pipeline Issues

#### Enable Debug Logging

```yaml
- name: Enable Debug Mode
  run: echo "ACTIONS_STEP_DEBUG=true" >> $GITHUB_ENV

- name: Debug Environment
  run: |
    echo "Current directory: $(pwd)"
    echo "Environment variables:"
    env | sort
    echo "AWS CLI version: $(aws --version)"
    echo "Terraform version: $(terraform version)"
```

#### Common Debug Commands

```bash
# Check GitHub Actions logs
gh run list --limit 10
gh run view <run-id> --log

# Check AWS resources
aws sts get-caller-identity
aws s3 ls
aws lambda list-functions

# Check Terraform state
terraform show
terraform state list
terraform output
```

### Best Practices Summary

1. **Modularize**: Keep pipeline stages small and focused
2. **Cache**: Use dependency and build artifact caching
3. **Parallelize**: Run independent jobs simultaneously
4. **Secure**: Manage secrets properly and scan for vulnerabilities
5. **Monitor**: Track metrics and set up alerting
6. **Test**: Include comprehensive testing at each stage
7. **Rollback**: Have quick rollback procedures ready
8. **Document**: Keep runbooks and troubleshooting guides updated


2. Current Work:
The user successfully tested the frontend deployment pipeline after I fixed path issues (frontend code is in  client/ directory, not src/). The frontend pipeline now automatically triggers on pushes to dev/development branches when frontend files change and successfully deploys to S3 with CloudFront invalidation. The user then wanted to get the infrastructure pipeline running, specifically focusing on the development directory for testing deployment without touching other directories. I updated the Terraform backend configuration to use S3 state storage (dev-thealpinestudio-backend-tf bucket) instead of local state, and ensured the infrastructure pipeline uses the correct working directory ( terraform/development/dev-env/). However, multiple issues arose during infrastructure deployment that were systematically resolved: (1) workflow visibility issue, (2) deprecated GitHub Actions versions, (3) AWS region mismatch between workflow (us-east-1) and S3 bucket (us-west-2), (4) Terraform workspace conflicts, (5) Terraform plan hanging due to missing variable inputs, (6) terraform.tfvars file not found error (fixed by using environment variables instead), (7) AWS provider profile error (fixed by switching to access_key/secret_key authentication), and (8) stale plan error (fixed by using direct apply instead of plan file). The most recent and critical issue was circular dependencies between Lambda and API Gateway modules where both modules were trying to create lambda permissions for the same functions, causing "Function not found" and "NoSuchBucket" errors. I just resolved this by removing all lambda permissions from the lambda module and letting the API Gateway module handle them exclusively, eliminating the circular dependency variables (api_gateway_arn,  api_gateway_id,  lambda_function_arn) from the lambda module.

3. Key Technical Concepts:
GitHub Actions workflows with modular design principles and automatic/manual triggers
AWS Lambda functions as independent microservices (not monolithic backend)
React frontend deployment to S3 with CloudFront invalidation
Terraform infrastructure as code with S3 backend state management
AWS services: S3, CloudFront, Lambda, API Gateway, DynamoDB, IAM, Cognito
CI/CD best practices: caching, parallelism, security, rollback strategies
Environment-specific deployments (development vs production)
GitHub secrets management vs OIDC authentication
Branch-based deployment strategies with path-based triggers
Terraform workspace management and backend configuration
API Gateway integration with frontend applications
Terraform variable management with .tfvars files
AWS region consistency across infrastructure components
GitHub Actions artifact management and job dependencies
Terraform module dependencies and circular dependency resolution
Lambda function deployment with Go runtime (provided.al2)
S3 bucket creation and CORS configuration for Lambda functions
Lambda permissions management through API Gateway module
4. Relevant Files and Code:
 .github/workflows/infrastructure-deployment.yml
Fixed multiple deployment issues including deprecated actions, region mismatches, workspace conflicts
Updated to use environment variables instead of tfvars file
Changed from AWS profile to access_key/secret_key authentication
Modified to use direct apply instead of plan file to avoid stale plan errors
Current configuration uses all required TF_VAR environment variables for consistency
 terraform/development/dev-env/main.tf
FIXED: Removed circular dependency between lambda and api_gateway modules
Lambda module no longer references API Gateway outputs
Simplified lambda module call with only required variables:
 terraform/development/dev-env/backend.tf
Updated S3 backend region from us-east-1 to us-west-2
Uses dev-thealpinestudio-backend-tf bucket for state storage
 terraform/development/dev-modules/dev-lambda/variables.tf
FIXED: Removed circular dependency variables (api_gateway_arn,  api_gateway_id,  lambda_function_arn)
Replaced with comments explaining the change
Lambda module now uses  account_id instead of  api_gateway_account_id for IAM resources
 terraform/development/dev-modules/dev-lambda/ (all lambda function files)
FIXED: Removed all aws_lambda_permission resources from lambda module
Replaced with comments: "Lambda permissions are handled by the API Gateway module to avoid circular dependencies"
Files affected: list-products.tf, checkout-products.tf, contact-email.tf, webhooks-confirm-checkout.tf
 terraform/development/dev-modules/dev-lambda/lambda.tf
FIXED: Updated IAM policy resources to use var.account_id instead of var.api_gateway_account_id
5. Problem Solving:
Workflow Visibility: Infrastructure workflow not appearing in GitHub Actions - resolved by committing and pushing changes
Deprecated Actions: Fixed upload-artifact@v3 and download-artifact@v3 errors by updating to v4 versions
AWS Region Mismatch: Resolved S3 bucket location error by changing workflow and backend configuration from us-east-1 to us-west-2
Terraform Workspace Conflicts: Eliminated TF_WORKSPACE environment variable conflicts by removing workspace management
Terraform Plan Hanging: Fixed 20+ minute hang by adding terraform.tfvars file usage and variable overrides
Terraform.tfvars Not Found: Fixed by removing dependency on gitignored tfvars file and using TF_VAR environment variables instead
AWS Provider Profile Error: Fixed by switching from AWS profile to access_key/secret_key authentication for CI/CD compatibility
Stale Plan Error: Fixed by using terraform apply -auto-approve directly instead of applying a plan file
Circular Dependencies (RESOLVED): Fixed the critical circular dependency issue between Lambda and API Gateway modules by:
Removing all lambda permissions from the lambda module
Letting the API Gateway module handle all lambda permissions exclusively
Removing circular dependency variables (api_gateway_arn,  api_gateway_id,  lambda_function_arn) from lambda module
Updating IAM resources to use  account_id instead of  api_gateway_account_id
6. Pending Tasks and Next Steps:
Test Infrastructure Deployment: The circular dependency fix has been committed and pushed. The next step is to monitor the GitHub Actions infrastructure deployment workflow to verify that the Lambda functions and S3 buckets are created successfully without the previous "Function not found" and "NoSuchBucket" errors.
API Gateway URL Integration: After infrastructure deployment succeeds, need to capture the API Gateway URL from Terraform outputs and update the frontend to use the real API Gateway URL instead of the hardcoded https://api-dev.thealpinestudio.com. The user emphasized: "we need to make sure that our frontend uses the api routes when our api gateway is created. does that make sense?"
End-to-End Testing: Once both infrastructure and frontend are deployed, need to test the complete integration between the React frontend and the deployed API Gateway with Lambda functions to ensure the development environment is fully functional.