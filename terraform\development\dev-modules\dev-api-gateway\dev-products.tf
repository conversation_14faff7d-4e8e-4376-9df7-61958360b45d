# API Gateway resource for individual product lookup: /dev-products/{id}
# This routes to the same Lambda function as dev-list-products but handles individual product requests

resource "aws_api_gateway_resource" "dev_products_resource" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  parent_id   = aws_api_gateway_rest_api.rest_api.root_resource_id
  path_part   = "dev-products"
}

# Resource for /dev-products/{id}
resource "aws_api_gateway_resource" "dev_products_id_resource" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  parent_id   = aws_api_gateway_resource.dev_products_resource.id
  path_part   = "{id}"
}

# GET method for /dev-products/{id}
resource "aws_api_gateway_method" "dev_products_get_method" {
  rest_api_id   = aws_api_gateway_rest_api.rest_api.id
  resource_id   = aws_api_gateway_resource.dev_products_id_resource.id
  http_method   = "GET"
  authorization = "none"
  
  request_parameters = {
    "method.request.path.id" = true
  }
}

# Integration for GET /dev-products/{id} - routes to the same Lambda function
resource "aws_api_gateway_integration" "dev_products_get_method_integration" {
  rest_api_id             = aws_api_gateway_rest_api.rest_api.id
  resource_id             = aws_api_gateway_resource.dev_products_id_resource.id
  http_method             = aws_api_gateway_method.dev_products_get_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/${var.list_lambda_function_arn}/invocations"

  depends_on = [
    aws_lambda_permission.lambda_api_gw_permissions_dev_products_get
  ]
}

# Method response for GET /dev-products/{id}
resource "aws_api_gateway_method_response" "dev_products_get_method_response_200" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  resource_id = aws_api_gateway_resource.dev_products_id_resource.id
  http_method = aws_api_gateway_method.dev_products_get_method.http_method
  status_code = "200"
  
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

# Integration response for GET /dev-products/{id}
resource "aws_api_gateway_integration_response" "dev_products_get_method_integration_response_200" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  resource_id = aws_api_gateway_resource.dev_products_id_resource.id
  http_method = aws_api_gateway_integration.dev_products_get_method_integration.http_method
  status_code = aws_api_gateway_method_response.dev_products_get_method_response_200.status_code
  
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token,X-Requested-With'"
    "method.response.header.Access-Control-Allow-Methods"     = "'OPTIONS,GET'"
    "method.response.header.Access-Control-Allow-Origin"      = "'https://test.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}

# Lambda permission for GET /dev-products/{id}
resource "aws_lambda_permission" "lambda_api_gw_permissions_dev_products_get" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.list_products_function}-dev-products"
  action        = "lambda:InvokeFunction"
  function_name = var.list_products_function
  principal     = "apigateway.amazonaws.com"
  source_arn    = "arn:aws:execute-api:us-west-2:410468036355:${aws_api_gateway_rest_api.rest_api.id}/*/GET/dev-products/*"
}

# OPTIONS method for CORS preflight on /dev-products/{id}
resource "aws_api_gateway_method" "dev_products_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.rest_api.id
  resource_id   = aws_api_gateway_resource.dev_products_id_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

# Integration for OPTIONS /dev-products/{id}
resource "aws_api_gateway_integration" "dev_products_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.rest_api.id
  resource_id          = aws_api_gateway_resource.dev_products_id_resource.id
  http_method          = aws_api_gateway_method.dev_products_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "NEVER"
  
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
  
  timeout_milliseconds = 29000
  depends_on           = [aws_api_gateway_method.dev_products_options_method]
}

# Method response for OPTIONS /dev-products/{id}
resource "aws_api_gateway_method_response" "dev_products_options_response" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  resource_id = aws_api_gateway_resource.dev_products_id_resource.id
  http_method = aws_api_gateway_method.dev_products_options_method.http_method
  status_code = 200
  depends_on  = [aws_api_gateway_method.dev_products_options_method]
  
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"      = true,
    "method.response.header.Access-Control-Allow-Methods"     = true,
    "method.response.header.Access-Control-Allow-Headers"     = true,
    "method.response.header.Access-Control-Allow-Credentials" = true,
    "method.response.header.Access-Control-Expose-Headers"    = true
  }
  
  response_models = {
    "application/json" = "Empty"
  }
}

# Integration response for OPTIONS /dev-products/{id}
resource "aws_api_gateway_integration_response" "dev_products_options_integration_response" {
  depends_on  = [aws_api_gateway_integration.dev_products_options_integration, aws_api_gateway_method_response.dev_products_options_response]
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  resource_id = aws_api_gateway_resource.dev_products_id_resource.id
  http_method = aws_api_gateway_method.dev_products_options_method.http_method
  status_code = 200
  
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'"
    "method.response.header.Access-Control-Allow-Methods"     = "'OPTIONS,GET'"
    "method.response.header.Access-Control-Allow-Origin"      = "'https://test.thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}
