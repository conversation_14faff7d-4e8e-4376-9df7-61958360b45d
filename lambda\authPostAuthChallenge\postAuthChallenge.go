package main

import (
    "context"
    "fmt"
    "log"
    "os"

    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
    "github.com/aws/aws-sdk-go/service/dynamodb"
)

var (
    region    = os.Getenv("AWS_REGION")
    tableName = os.Getenv("TOTP_TABLE_NAME")
    userPoolID = os.Getenv("USER_POOL_ID")
)

type Event struct {
    Version       string `json:"version"`
    Region        string `json:"region"`
    UserPoolID    string `json:"userPoolId"`
    UserName      string `json:"userName"`
    CallerContext struct {
        AwsSdkVersion string `json:"awsSdkVersion"`
        ClientId      string `json:"clientId"`
    } `json:"callerContext"`
    TriggerSource string `json:"triggerSource"`
    Request       struct {
        UserAttributes map[string]string `json:"userAttributes"`
    } `json:"request"`
    Response struct {
        Version string `json:"version"`
    } `json:"response"`
}

func handlePostAuthentication(ctx context.Context, event Event) (Event, error) {
    log.Println("Post authentication event triggered")
    log.Printf("Using DynamoDB table: %s", tableName)

    email := event.Request.UserAttributes["email"]
    if email == "" {
        return event, fmt.Errorf("user email not found")
    }

    log.Printf("Post-authentication process for email: %s", email)

    // Check if the user has completed TOTP setup
    sess, err := session.NewSession(&aws.Config{
        Region: aws.String(region),
    })
    if err != nil {
        log.Printf("Failed to create AWS session: %v", err)
        return event, err
    }

    // Check DynamoDB for TOTP setup
    dynaSvc := dynamodb.New(sess)
    input := &dynamodb.GetItemInput{
        TableName: aws.String(tableName),
        Key: map[string]*dynamodb.AttributeValue{
            "email": {
                S: aws.String(email),
            },
        },
    }

    log.Printf("DynamoDB GetItem input: %+v", input)

    result, err := dynaSvc.GetItem(input)
    if err != nil {
        log.Printf("Error getting item from DynamoDB: %v", err)
        return event, fmt.Errorf("failed to get item from DynamoDB: %v", err)
    }

    log.Printf("DynamoDB GetItem result: %+v", result)

    if result.Item == nil {
        log.Println("TOTP setup not found, proceeding with setup instructions")
        // Add logic here to handle users without TOTP setup
        // This could include sending an email with setup instructions or redirecting them to a TOTP setup page
        
        // For example, you can add a custom attribute to user attributes indicating the need for TOTP setup
        svc := cognitoidentityprovider.New(sess)
        _, err := svc.AdminUpdateUserAttributes(&cognitoidentityprovider.AdminUpdateUserAttributesInput{
            UserPoolId: aws.String(userPoolID),
            Username:   aws.String(email),
            UserAttributes: []*cognitoidentityprovider.AttributeType{
                {
                    Name:  aws.String("custom:totpSetupRequired"),
                    Value: aws.String("true"),
                },
            },
        })
        if err != nil {
            log.Printf("Error updating user attributes: %v", err)
            return event, fmt.Errorf("failed to update user attributes: %v", err)
        }

        log.Printf("User attributes updated for TOTP setup requirement for email: %s", email)
    } else {
        log.Println("TOTP setup found for user")
    }

    event.Response.Version = "1"
    return event, nil
}

func main() {
    lambda.Start(handlePostAuthentication)
}
