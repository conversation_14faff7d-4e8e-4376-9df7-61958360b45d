# Alpine Studio - AWS Secrets Manager Migration Plan

## Current State
- Secrets stored in GitHub repository secrets
- Environment variables hardcoded in workflows
- Manual secret management across environments

## Target State
- All sensitive data in AWS Secrets Manager
- Environment-specific secret retrieval
- Automated secret rotation where possible

## Migration Steps

### Phase 1: Create Secrets in AWS Secrets Manager

```bash
# Development Environment Secrets
aws secretsmanager create-secret \
  --name "alpinestudio/dev/stripe" \
  --description "Stripe keys for development environment" \
  --secret-string '{
    "public_key": "pk_test_...",
    "secret_key": "sk_test_...",
    "webhook_secret": "whsec_..."
  }'

aws secretsmanager create-secret \
  --name "alpinestudio/dev/api" \
  --description "API configuration for development" \
  --secret-string '{
    "gateway_id": "u3vj4dkndh",
    "admin_gateway_id": "f2trvidc5b",
    "resend_api_key": "re_..."
  }'

# Production Environment Secrets
aws secretsmanager create-secret \
  --name "alpinestudio/prod/stripe" \
  --description "Stripe keys for production environment" \
  --secret-string '{
    "public_key": "pk_live_...",
    "secret_key": "sk_live_...",
    "webhook_secret": "whsec_..."
  }'

aws secretsmanager create-secret \
  --name "alpinestudio/prod/api" \
  --description "API configuration for production" \
  --secret-string '{
    "gateway_id": "prod_gateway_id",
    "admin_gateway_id": "prod_admin_gateway_id",
    "resend_api_key": "re_..."
  }'
```

### Phase 2: Update Terraform to Use Secrets Manager

```hcl
# terraform/modules/secrets/main.tf
data "aws_secretsmanager_secret_version" "stripe" {
  secret_id = "alpinestudio/${var.environment}/stripe"
}

data "aws_secretsmanager_secret_version" "api" {
  secret_id = "alpinestudio/${var.environment}/api"
}

locals {
  stripe_secrets = jsondecode(data.aws_secretsmanager_secret_version.stripe.secret_string)
  api_secrets = jsondecode(data.aws_secretsmanager_secret_version.api.secret_string)
}

# Pass to Lambda environment variables
resource "aws_lambda_function" "checkout" {
  environment {
    variables = {
      STRIPE_SECRET_KEY = local.stripe_secrets.secret_key
      STRIPE_WEBHOOK_SECRET = local.stripe_secrets.webhook_secret
    }
  }
}
```

### Phase 3: Update GitHub Actions Workflows

```yaml
# .github/workflows/dev-deployment.yml
- name: Get secrets from AWS Secrets Manager
  run: |
    STRIPE_SECRETS=$(aws secretsmanager get-secret-value --secret-id "alpinestudio/dev/stripe" --query SecretString --output text)
    API_SECRETS=$(aws secretsmanager get-secret-value --secret-id "alpinestudio/dev/api" --query SecretString --output text)
    
    echo "STRIPE_PUBLIC_KEY=$(echo $STRIPE_SECRETS | jq -r .public_key)" >> $GITHUB_ENV
    echo "STRIPE_SECRET_KEY=$(echo $STRIPE_SECRETS | jq -r .secret_key)" >> $GITHUB_ENV
    echo "RESEND_API_KEY=$(echo $API_SECRETS | jq -r .resend_api_key)" >> $GITHUB_ENV
```

## Secrets Organization Structure

```
alpinestudio/
├── dev/
│   ├── stripe          # Development Stripe keys
│   ├── api             # API keys and configurations
│   └── database        # Database credentials (if needed)
├── prod/
│   ├── stripe          # Production Stripe keys
│   ├── api             # API keys and configurations
│   └── database        # Database credentials (if needed)
└── shared/
    ├── aws             # AWS account-level secrets
    └── infrastructure  # Infrastructure secrets
```

## Benefits After Migration

1. **Security**: Encrypted at rest, automatic rotation
2. **Compliance**: Audit trails, access logging
3. **Scalability**: Easy to add new environments
4. **Maintenance**: Centralized secret management
5. **Cost**: More cost-effective than GitHub secrets for production

## Rollback Plan

1. Keep GitHub secrets as backup during migration
2. Test each environment thoroughly
3. Gradual migration (dev first, then prod)
4. Document all changes for easy rollback
