package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
)

type RequestBody struct {
	Title string `json:"title"`
}

func markProductAsSold(request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Retrieve product ID from path parameters
	productID := request.PathParameters["id"]

	if productID == "" {
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusBadRequest,
			Body:       "Missing product ID",
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "https://admin.thealpinestudio.com",
				"Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
				"Access-Control-Allow-Headers":     "Content-Type,Authorization",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	// Parse the request body to get the title
	var requestBody RequestBody
	err := json.Unmarshal([]byte(request.Body), &requestBody)
	if err != nil || requestBody.Title == "" {
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusBadRequest,
			Body:       "Missing or invalid title in request body",
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "https://admin.thealpinestudio.com",
				"Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
				"Access-Control-Allow-Headers":     "Content-Type,Authorization",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	// Initialize AWS clients
	sess := session.Must(session.NewSession())
	dynamoClient := dynamodb.New(sess)

	// Update the product in DynamoDB to mark it as sold
	_, err = dynamoClient.UpdateItem(&dynamodb.UpdateItemInput{
		TableName: aws.String(os.Getenv("TABLE_NAME")),
		Key: map[string]*dynamodb.AttributeValue{
			"ID":    {S: aws.String(productID)},          // Partition Key
			"Title": {S: aws.String(requestBody.Title)}, // Sort Key
		},
		UpdateExpression: aws.String("set sold = :s"),
		ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
			":s": {BOOL: aws.Bool(true)},
		},
		ReturnValues: aws.String("UPDATED_NEW"),
	})

	if err != nil {
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       fmt.Sprintf("Failed to mark product as sold: %v", err),
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "https://admin.thealpinestudio.com",
				"Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
				"Access-Control-Allow-Headers":     "Content-Type,Authorization",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	// Return success response with necessary CORS headers
	return events.APIGatewayProxyResponse{
		StatusCode: http.StatusOK,
		Body:       fmt.Sprintf("Product %s marked as sold", productID),
		Headers: map[string]string{
			"Access-Control-Allow-Origin":      "https://admin.thealpinestudio.com",
			"Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
			"Access-Control-Allow-Headers":     "Content-Type,Authorization",
			"Access-Control-Allow-Credentials": "true",
		},
	}, nil
}

func main() {
	lambda.Start(markProductAsSold)
}
