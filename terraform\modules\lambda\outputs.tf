output "lambda_function_arn" {
  value = aws_lambda_function.list_products_function.invoke_arn
}
output "lambda_function_name" {
  value = aws_lambda_function.list_products_function.function_name
}
output "lambda_function_checkout_arn" {
  description = "The ARN of the checkout lambda function"
  value       = aws_lambda_function.checkout_products_function.invoke_arn
}

output "lambda_function_webhooks_arn" {
  description = "The ARN of the webhooks lambda function"
  value       = aws_lambda_function.webhooks_function.invoke_arn
}

output "lambda_function_contact_arn" {
  description = "The ARN of the contact email lambda function"
  value       = aws_lambda_function.contact_email_function.invoke_arn
}