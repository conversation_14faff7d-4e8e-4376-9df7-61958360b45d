# Terraform Migration and Pipeline Plan

## Phase 1: Module Restructuring

### 1. Create New Directory Structure
- [ ] Create new directory structure:
  ```
  terraform/
  ├── environments/
  │   ├── production/    # Current "testing" environment
  │   ├── testing/       # New testing environment
  │   └── admin/         # Admin environment
  ├── modules/
  │   ├── lambda/
  │   ├── api-gateway/
  │   ├── cognito/
  │   ├── s3/
  │   └── dynamodb/
  └── pipelines/
      ├── production.yml
      ├── testing.yml
      └── admin.yml
  ```

### 2. Refactor Common Modules
- [ ] Create base modules with environment-specific configurations:
  - [ ] Lambda module with common configurations
  - [ ] API Gateway module with shared resources
  - [ ] S3 module for both hosting and lambda storage
  - [ ] Cognito module for authentication
  - [ ] DynamoDB module for data storage

### 3. Update Resource Naming
- [ ] Create naming convention for all resources:
  ```
  {environment}-{service}-{resource}
  Example: prod-lambda-checkout
  ```
- [ ] Update existing resource names in current "testing" environment to "production"
- [ ] Create new testing environment with proper naming convention

## Phase 2: State Migration

### 1. Production (Current "testing" environment)
- [ ] Create new S3 bucket: `thealpinestudio-terraform-production`
- [ ] Export current state from `testing-thealpinestudio-backend-tf`
- [ ] Import state to new production bucket
- [ ] Update backend configuration

### 2. New Testing Environment
- [ ] Create new S3 bucket: `thealpinestudio-terraform-testing`
- [ ] Set up new backend configuration
- [ ] Initialize new state

### 3. Admin Environment
- [ ] Create new S3 bucket: `thealpinestudio-terraform-admin`
- [ ] Export current state from `admin-thealpinestudio-backend-tf`
- [ ] Import state to new admin bucket
- [ ] Update backend configuration

## Phase 3: Pipeline Creation

### 1. Production Pipeline
```yaml
- [ ] Create `.github/workflows/terraform-production.yml`:
  - Deploy to production environment
  - Run on main branch merge
  - Require approval for apply
  - Include post-deployment tests
  - Update CloudFront distribution

### 2. Testing Pipeline
```yaml
- [ ] Create `.github/workflows/terraform-testing.yml`:
  - Deploy to testing environment
  - Run on pull requests to main
  - Auto-apply changes
  - Include integration tests
  - Update test CloudFront distribution

### 3. Admin Pipeline
```yaml
- [ ] Create `.github/workflows/terraform-admin.yml`:
  - Deploy to admin environment
  - Run on admin branch changes
  - Require approval for apply
  - Include admin-specific tests
  - Update admin CloudFront distribution

## Phase 4: Environment Variables and Secrets

### 1. Production Environment
- [ ] Set up production environment variables:
  - [ ] AWS credentials
  - [ ] Stripe keys
  - [ ] Frontend URLs
  - [ ] Other sensitive data

### 2. Testing Environment
- [ ] Set up testing environment variables:
  - [ ] Separate AWS credentials
  - [ ] Test Stripe keys
  - [ ] Test frontend URLs

### 3. Admin Environment
- [ ] Set up admin environment variables:
  - [ ] Admin AWS credentials
  - [ ] Admin-specific secrets
  - [ ] Admin frontend URLs

## Phase 5: Testing and Validation

### 1. Production Environment
- [ ] Create test suite for production:
  - [ ] API endpoint tests
  - [ ] Lambda function tests
  - [ ] S3 bucket access tests
  - [ ] Cognito authentication tests

### 2. Testing Environment
- [ ] Create test suite for testing:
  - [ ] Integration tests
  - [ ] Load tests
  - [ ] Security tests

### 3. Admin Environment
- [ ] Create test suite for admin:
  - [ ] Admin API tests
  - [ ] Authentication tests
  - [ ] Permission tests

## Phase 6: Documentation

### 1. Update Documentation
- [ ] Create detailed README for each environment
- [ ] Document pipeline processes
- [ ] Create runbook for common operations
- [ ] Document rollback procedures

### 2. Create Migration Guide
- [ ] Document steps for future migrations
- [ ] Include troubleshooting guide
- [ ] Add monitoring and alerting setup

## Phase 7: Cleanup

### 1. Remove Old Resources
- [ ] Archive old terraform configurations
- [ ] Remove unused S3 buckets
- [ ] Clean up old IAM roles
- [ ] Remove deprecated Lambda functions

### 2. Update DNS and Routing
- [ ] Update Route53 records
- [ ] Update CloudFront distributions
- [ ] Update API Gateway endpoints