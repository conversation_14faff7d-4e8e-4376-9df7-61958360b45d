package main

import (
    "context"
    "fmt"
    "log"
    "os"
    "time"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/dynamodb"
    "github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
)

var (
    dynamoTable = os.Getenv("TOTP_TABLE_NAME")
    region      = os.Getenv("AWS_REGION")
)



type User struct {
    Email       string `json:"email"`
    PhoneNumber string `json:"phone_number"`
    Name        string `json:"name"`
    CreatedAt   string `json:"created_at"`
}

func postConfirmationHandler(ctx context.Context, event events.CognitoEventUserPoolsPostConfirmation) (events.CognitoEventUserPoolsPostConfirmation, error) {
    log.Println("Post confirmation event triggered")

    userAttributes := event.Request.UserAttributes
    log.Printf("User attributes received: %v", userAttributes)

    email, emailExists := userAttributes["email"]
    phoneNumber, phoneNumberExists := userAttributes["phone_number"]
    name, nameExists := userAttributes["name"]

    log.Printf("Extracted attributes: email=%s, phone_number=%s, name=%s", email, phoneNumber, name)

    if !emailExists || !phoneNumberExists || !nameExists {
        log.Printf("Missing required attributes: emailExists=%v, phoneNumberExists=%v, nameExists=%v", emailExists, phoneNumberExists, nameExists)
        return event, fmt.Errorf("missing required attributes: email=%v, phone_number=%v, name=%v", emailExists, phoneNumberExists, nameExists)
    }

    log.Printf("Storing user with email %s and phone number %s in DynamoDB", email, phoneNumber)

    sess, err := session.NewSession(&aws.Config{
        Region: aws.String(region),
    })
    if err != nil {
        log.Printf("Failed to create AWS session: %v", err)
        return event, err
    }

    err = storeUserInDynamoDB(sess, email, phoneNumber, name)
    if err != nil {
        log.Printf("Failed to store user in DynamoDB: %v", err)
        return event, fmt.Errorf("failed to store user in DynamoDB: %v", err)
    }

    log.Println("User stored in DynamoDB successfully")

    return event, nil
}

func storeUserInDynamoDB(sess *session.Session, email, phoneNumber, name string) error {
    svc := dynamodb.New(sess)

    user := User{
        Email:       email,
        PhoneNumber: phoneNumber,
        Name:        name,
        CreatedAt:   time.Now().Format(time.RFC3339),
    }

    av, err := dynamodbattribute.MarshalMap(user)
    if err != nil {
        return fmt.Errorf("failed to marshal user: %v", err)
    }

    input := &dynamodb.PutItemInput{
        TableName: aws.String(dynamoTable),
        Item:      av,
    }

    _, err = svc.PutItem(input)
    if err != nil {
        return fmt.Errorf("failed to put item in DynamoDB: %v", err)
    }

    return nil
}

func main() {
    lambda.Start(postConfirmationHandler)
}
