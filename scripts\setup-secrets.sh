#!/bin/bash

# Setup AWS Secrets Manager secrets for The Alpine Studio
# Run this script to create all necessary secrets in AWS Secrets Manager

set -e

AWS_REGION="us-west-2"
AWS_PROFILE="thealpinestudio"

echo "🔐 Setting up AWS Secrets Manager secrets for The Alpine Studio"
echo "Using AWS region: $AWS_REGION"
echo "Using AWS profile: $AWS_PROFILE"
echo ""

# Function to create or update a secret
create_or_update_secret() {
    local secret_name=$1
    local secret_description=$2
    local secret_value=$3
    
    echo "Setting up secret: $secret_name"
    
    # Try to update existing secret first
    if aws secretsmanager put-secret-value \
        --secret-id "$secret_name" \
        --secret-string "$secret_value" \
        --region "$AWS_REGION" \
        --profile "$AWS_PROFILE" >/dev/null 2>&1; then
        echo "✅ Updated existing secret: $secret_name"
    else
        # Create new secret if update failed
        if aws secretsmanager create-secret \
            --name "$secret_name" \
            --description "$secret_description" \
            --secret-string "$secret_value" \
            --region "$AWS_REGION" \
            --profile "$AWS_PROFILE" >/dev/null 2>&1; then
            echo "✅ Created new secret: $secret_name"
        else
            echo "❌ Failed to create/update secret: $secret_name"
        fi
    fi
}

# Prompt for secrets
echo "Please provide the following secret values:"
echo ""

read -p "Stripe Publishable Key (pk_test_...): " STRIPE_PUBLISHABLE_KEY
read -s -p "Stripe Secret Key (sk_test_...): " STRIPE_SECRET_KEY
echo ""
read -s -p "Stripe Webhook Endpoint Secret (whsec_...): " STRIPE_ENDPOINT_SECRET
echo ""
read -s -p "Resend API Key: " RESEND_API_KEY
echo ""

# Validate inputs
if [[ -z "$STRIPE_PUBLISHABLE_KEY" || -z "$STRIPE_SECRET_KEY" || -z "$STRIPE_ENDPOINT_SECRET" || -z "$RESEND_API_KEY" ]]; then
    echo "❌ All secret values are required!"
    exit 1
fi

echo ""
echo "Creating development secrets..."

# Development secrets
create_or_update_secret "dev/stripe/publishable-key" "Stripe publishable key for development" "$STRIPE_PUBLISHABLE_KEY"
create_or_update_secret "dev/stripe/secret-key" "Stripe secret key for development" "$STRIPE_SECRET_KEY"
create_or_update_secret "dev/stripe/endpoint-secret" "Stripe webhook endpoint secret for development" "$STRIPE_ENDPOINT_SECRET"
create_or_update_secret "dev/resend/api-key" "Resend API key for development" "$RESEND_API_KEY"

echo ""
echo "Creating production secrets..."

# Production secrets (you might want different values for production)
create_or_update_secret "prod/stripe/publishable-key" "Stripe publishable key for production" "$STRIPE_PUBLISHABLE_KEY"
create_or_update_secret "prod/stripe/secret-key" "Stripe secret key for production" "$STRIPE_SECRET_KEY"
create_or_update_secret "prod/stripe/endpoint-secret" "Stripe webhook endpoint secret for production" "$STRIPE_ENDPOINT_SECRET"
create_or_update_secret "prod/resend/api-key" "Resend API key for production" "$RESEND_API_KEY"

echo ""
echo "🎉 All secrets have been set up in AWS Secrets Manager!"
echo ""
echo "Note: The 'dev/frontend/api-gateway-url' secret will be automatically"
echo "created by the infrastructure deployment pipeline."
echo ""
echo "You can now:"
echo "1. Remove the GitHub secrets (STRIPE_SECRET_KEY, STRIPE_ENDPOINT_SECRET, etc.)"
echo "2. Run your infrastructure and frontend deployments"
echo "3. The pipelines will automatically use AWS Secrets Manager"
