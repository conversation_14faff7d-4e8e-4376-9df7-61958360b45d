data "aws_caller_identity" "current" {}

# SES Email Identity
resource "aws_ses_email_identity" "admin_ses_email_identity_tj" {
  email = "<EMAIL>"
}

resource "aws_ses_email_identity" "admin_ses_email_identity_ariel" {
  email = "<EMAIL>"
}

# SES Domain Identity
resource "aws_ses_domain_identity" "thealpinestudio_identity" {
  domain = "thealpinestudio.com"
}

# SES Domain Mail From
resource "aws_ses_domain_mail_from" "mail_from_thealpinestudio" {
  domain           = aws_ses_domain_identity.thealpinestudio_identity.domain
  mail_from_domain = "bounce.thealpinestudio.com"
}

# SES Domain DKIM
resource "aws_ses_domain_dkim" "thealpinestudio_dkim" {
  domain = aws_ses_domain_identity.thealpinestudio_identity.domain
}

# Route 53 DKIM Records
resource "aws_route53_record" "thealpinestudio_amazonses_dkim_record" {
  count   = 3
  zone_id = var.zone_id
  name    = "${aws_ses_domain_dkim.thealpinestudio_dkim.dkim_tokens[count.index]}._domainkey"
  type    = "CNAME"
  ttl     = "600"
  records = ["${aws_ses_domain_dkim.thealpinestudio_dkim.dkim_tokens[count.index]}.dkim.amazonses.com"]

  lifecycle {
    ignore_changes = [
      name,
      records,
    ]
  }
}

# Route 53 MAIL FROM MX Record
resource "aws_route53_record" "ses_domain_mail_from_mx" {
  zone_id = var.zone_id
  name    = aws_ses_domain_mail_from.mail_from_thealpinestudio.mail_from_domain
  type    = "MX"
  ttl     = "600"
  records = ["10 feedback-smtp.us-west-2.amazonses.com"]

  lifecycle {
    ignore_changes = [
      name,
      records,
    ]
  }
}

# Route 53 MAIL FROM SPF Record
resource "aws_route53_record" "ses_domain_mail_from_spf" {
  zone_id = var.zone_id
  name    = aws_ses_domain_mail_from.mail_from_thealpinestudio.mail_from_domain
  type    = "TXT"
  ttl     = "600"
  records = ["v=spf1 include:amazonses.com ~all"]

  lifecycle {
    ignore_changes = [
      name,
      records,
    ]
  }
}

# Route 53 DMARC Record
resource "aws_route53_record" "dmarc_record" {
  zone_id = var.zone_id
  name    = "_dmarc.thealpinestudio.com"
  type    = "TXT"
  ttl     = "600"
  records = ["v=DMARC1; p=none; rua=mailto:<EMAIL>; ruf=mailto:<EMAIL>; sp=none; aspf=s; adkim=s;"]

  lifecycle {
    ignore_changes = [
      name,
      records,
    ]
  }

}

//USER POOL COGNITO
resource "aws_cognito_user_pool" "admin_user_pool_v2" {
  name = "admin_user_pool_v2"

  mfa_configuration = "OPTIONAL"


  password_policy {
    minimum_length                   = 8
    require_lowercase                = true
    require_numbers                  = true
    require_symbols                  = true
    require_uppercase                = true
    temporary_password_validity_days = 7
  }


  software_token_mfa_configuration {
    enabled = true
  }

  schema {
    name                = "email"
    attribute_data_type = "String"
    mutable             = false
    string_attribute_constraints {
      min_length = 1
      max_length = 256
    }
  }

  account_recovery_setting {
    recovery_mechanism {
      name     = "verified_email"
      priority = 1
    }

  }

  email_configuration {
    email_sending_account = "DEVELOPER"
    source_arn            = "arn:aws:ses:us-west-2:${data.aws_caller_identity.current.account_id}:identity/<EMAIL>"
    from_email_address    = "<EMAIL>"
  }



  lambda_config {
    pre_sign_up = var.auth_cognito_pre_signup_function_arn
    # create_auth_challenge          = var.auth_cognito_create_auth_challenge_function_arn
    # define_auth_challenge          = var.auth_cognito_define_auth_challenge_function_arn
    post_authentication = var.auth_cognito_post_authentication_function_arn
    post_confirmation   = var.auth_cognito_post_confirmation_function_arn
    pre_authentication  = var.auth_cognito_pre_authentication_function_arn
    # verify_auth_challenge_response = var.auth_cognito_verify_auth_challenge_response_function_arn
  }

  admin_create_user_config {
    allow_admin_create_user_only = false
    invite_message_template {
      email_subject = "Set Up Your Admin Account"
      email_message = <<EOF
        You have been invited to create an admin account. Your username is {username}.
        Please set up your password by following this link: 
        https://admin.thealpinestudio.com/admin/setup-account?user={username}&code={####}&email={email}&phone_number={phone_number}
        EOF
      sms_message   = "Your admin account setup code is {####} for {username}"
    }

  }

  verification_message_template {
    default_email_option = "CONFIRM_WITH_CODE"
    email_message        = "Your verification code is {####}"
    email_subject        = "Your verification code"
    sms_message          = "Your verification code is {####}"
  }

  auto_verified_attributes = ["email"]


}

resource "aws_cognito_user_pool_client" "admin_user_pool_client" {
  name         = "admin_user_pool_client"
  user_pool_id = aws_cognito_user_pool.admin_user_pool_v2.id

  generate_secret = true

  allowed_oauth_flows_user_pool_client = true
  allowed_oauth_flows                  = ["code", "implicit"]
  allowed_oauth_scopes                 = ["email", "openid", "profile"]

  callback_urls = ["https://admin.thealpinestudio.com/callback"]
  logout_urls   = ["https://admin.thealpinestudio.com/logout"]

  explicit_auth_flows = [
    "ALLOW_CUSTOM_AUTH",
    "ALLOW_USER_PASSWORD_AUTH",
    "ALLOW_REFRESH_TOKEN_AUTH"
  ]
}

resource "aws_cognito_user_pool_domain" "admin_user_pool_domain_v2" {
  domain       = "admin-thealpinestudio-com"
  user_pool_id = aws_cognito_user_pool.admin_user_pool_v2.id
}

//PERMISSIONS
resource "aws_lambda_permission" "allow_cognito" {
  statement_id  = "AllowExecutionFromCognito"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_post_confirmation_function_name
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.admin_user_pool_v2.arn
}

resource "aws_lambda_permission" "allow_cognito_define_auth_challenge" {
  statement_id  = "AllowExecutionFromCognito"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_define_auth_challenge_function_name
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.admin_user_pool_v2.arn

}

resource "aws_lambda_permission" "allow_cognito_create_auth_challenge" {
  statement_id  = "AllowExecutionFromCognito"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_create_auth_challenge_function_name
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.admin_user_pool_v2.arn

}

resource "aws_lambda_permission" "allow_cognito_verify_auth_challenge_response" {
  statement_id  = "AllowExecutionFromCognito"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_verify_auth_challenge_response_function_name
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.admin_user_pool_v2.arn

}

resource "aws_lambda_permission" "allow_cognito_pre_signup_challenge_response" {
  statement_id  = "AllowExecutionFromCognito"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_pre_signup_function_arn
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.admin_user_pool_v2.arn

}

resource "aws_lambda_permission" "allow_cognito_post_authentication" {
  statement_id  = "AllowExecutionFromCognito"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_post_authentication_function_name
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.admin_user_pool_v2.arn

}

resource "aws_lambda_permission" "allow_cognito_pre_authentication" {
  statement_id  = "AllowExecutionFromCognito"
  action        = "lambda:InvokeFunction"
  function_name = var.auth_cognito_pre_authentication_function_arn
  principal     = "cognito-idp.amazonaws.com"
  source_arn    = aws_cognito_user_pool.admin_user_pool_v2.arn

}
// IAM Role
# IAM Role for Cognito
resource "aws_iam_role" "cognito_role" {
  name = "cognito_sns_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "cognito-idp.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# Policy for Cognito actions
resource "aws_iam_policy" "cognito_policy" {
  name = "CognitoPolicy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect : "Allow",
        Action : [
          "cognito-idp:AdminConfirmSignUp",
          "cognito-idp:AdminUpdateUserAttributes",
          "cognito-idp:ListUsers",
          "cognito-idp:DescribeUserPool",
          "cognito-idp:DescribeUserPoolClient",
          "cognito-idp:AdminGetUser",
          "cognito-idp:AdminInitiateAuth",
          "cognito-idp:AdminRespondToAuthChallenge",
          "cognito-idp:AdminSetUserMFAPreference",
          "cognito-idp:InitiateAuth",
          "cognito-idp:GetUser"
        ],
        Resource : "arn:aws:cognito-idp:${var.aws_region}:${data.aws_caller_identity.current.account_id}:userpool/${aws_cognito_user_pool.admin_user_pool_v2.id}"
      },
      {
        Effect : "Allow",
        Action : [
          "ses:SendEmail",
          "ses:SendRawEmail"
        ],
        Resource : "*"
      },
      {
        "Effect" : "Allow",
        "Action" : [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        "Resource" : "*"
      },
      {
        Effect : "Allow",
        Action : [
          "lambda:InvokeFunction"
        ],
        Resource : "arn:aws:lambda:${var.aws_region}:${data.aws_caller_identity.current.account_id}:function:*"
      }
    ]
  })
}

# Attach policies to the IAM role
resource "aws_iam_role_policy_attachment" "cognito_policy_attachment" {
  role       = aws_iam_role.cognito_role.name
  policy_arn = aws_iam_policy.cognito_policy.arn
}

//send ses email
# IAM Policy for SES Permissions
resource "aws_iam_policy" "lambda_ses_policy" {
  name        = "LambdaSESPolicy"
  description = "Policy to allow Lambda to send emails using SES"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "ses:SendEmail",
          "ses:SendRawEmail"
        ],
        Resource = [
          "arn:aws:ses:us-west-2:${data.aws_caller_identity.current.account_id}:identity/<EMAIL>",
          "arn:aws:ses:us-west-2:${data.aws_caller_identity.current.account_id}:identity/<EMAIL>"
        ]
      }
    ]
  })
}

# Attach SES Policy to Lambda Execution Role
resource "aws_iam_role_policy_attachment" "attach_lambda_ses_policy" {
  role       = "lambda_execution_role_auth_cognito_function" # Replace with your actual role name
  policy_arn = aws_iam_policy.lambda_ses_policy.arn
}


// Define an SNS Topic
resource "aws_sns_topic" "cognito_sns_topic" {
  name = var.sns_topic_name
}

// Policy for publishing to the SNS topic
resource "aws_iam_policy" "cognito_sns_policy" {
  name = "CognitoSNSPolicy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = "sns:Publish",
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "cognito_sns_attachment" {
  role       = aws_iam_role.cognito_role.name
  policy_arn = aws_iam_policy.cognito_sns_policy.arn
}

// Policy for SES to send emails
resource "aws_iam_policy" "cognito_ses_policy" {
  name = "CognitoSESPolicy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [

      {
        Effect = "Allow",
        Action = [
          "ses:SendEmail",
          "ses:SendRawEmail"
        ],
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "cognito_ses_attachment" {
  role       = aws_iam_role.cognito_role.name
  policy_arn = aws_iam_policy.cognito_ses_policy.arn
}

# IAM Role for Cognito PostConfirmation Trigger
resource "aws_iam_role" "cognito_post_confirmation_role" {
  name = "cognito_post_confirmation_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "lambda.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# Policy for Cognito and DynamoDB actions
resource "aws_iam_policy" "cognito_post_confirmation_policy" {
  name = "CognitoPostConfirmationPolicy"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect : "Allow",
        Action : [
          "cognito-idp:AdminConfirmSignUp",
          "cognito-idp:AdminUpdateUserAttributes",
          "cognito-idp:ListUsers",
          "cognito-idp:DescribeUserPoolClient",
          "cognito-idp:AdminGetUser",
          "cognito-idp:AdminUpdateUserAttributes",
          "cognito-idp:AdminSetUserMFAPreference",

        ],
        Resource : "arn:aws:cognito-idp:${var.aws_region}:${data.aws_caller_identity.current.account_id}:userpool/${aws_cognito_user_pool.admin_user_pool_v2.id}"
      },
      {
        Effect : "Allow",
        Action : [
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:GetItem",
          "dynamodb:Query"
        ],
        Resource : "arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/${var.dynamo_table_users_name}"
      },
      {
        Effect : "Allow",
        Action : [
          "lambda:InvokeFunction"
        ],
        Resource : "arn:aws:lambda:${var.aws_region}:${data.aws_caller_identity.current.account_id}:function:*"
      }
    ]
  })
}

# Attach policies to the IAM role
resource "aws_iam_role_policy_attachment" "cognito_post_confirmation_policy_attachment" {
  role       = aws_iam_role.cognito_post_confirmation_role.name
  policy_arn = aws_iam_policy.cognito_post_confirmation_policy.arn
}
