{"compilerOptions": {"sourceMap": true, "target": "es2015", "downlevelIteration": true, "allowJs": true, "jsx": "react", "module": "ESNext", "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "noResolve": false, "noImplicitAny": false, "experimentalDecorators": true, "declaration": true, "removeComments": true, "noImplicitReturns": true, "noUnusedLocals": true, "noEmit": false, "strict": true, "checkJs": true, "outDir": "build", "composite": true, "lib": ["dom", "dom.iterable", "esnext"], "baseUrl": "src", "paths": {"app/*": ["./app/*"]}, "allowSyntheticDefaultImports": true}, "include": ["src/**/*", "index.d.ts", "src/types/**/*", "src/data/categoriesData.ts"], "exclude": ["build", "node_modules"]}