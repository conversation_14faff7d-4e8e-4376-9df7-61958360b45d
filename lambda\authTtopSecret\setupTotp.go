package main

import (
    "context"
    "encoding/json"
    "fmt"
    "log"
    "net/http"
    "os"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/cognitoidentityprovider"
)

// Environment variables
var (
    region       = os.Getenv("AWS_REGION")
)

type SetupTOTPRequest struct {
    Email       string `json:"email"`
    AccessToken string `json:"accessToken"`
    UserCode    string `json:"userCode"`
}

type SetupTOTPResponse struct {
    Message string `json:"message"`
}


func setupTOTPHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    log.Printf("Received request body: %s", request.Body)

    // Unmarshal the request body into the setupRequest struct
    var setupRequest SetupTOTPRequest
    err := json.Unmarshal([]byte(request.Body), &setupRequest)
    if (err != nil) {
        log.Printf("Error unmarshalling request body: %v", err)
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusBadRequest,
            Body:       fmt.Sprintf(`{"message": "Invalid request: %v"}`, err),
            Headers: map[string]string{
                "Access-Control-Allow-Origin":      "*",
                "Access-Control-Allow-Credentials": "true",
            },
        }, nil
    }

    log.Printf("Parsed request: %+v", setupRequest)

    // Validate request fields
    if setupRequest.Email == "" || setupRequest.AccessToken == "" || setupRequest.UserCode == "" {
        log.Printf("Error: Missing email, accessToken, or user code")
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusBadRequest,
            Body:       `{"message": "Missing email, accessToken, or user code"}`,
            Headers: map[string]string{
                "Access-Control-Allow-Origin":      "*",
                "Access-Control-Allow-Credentials": "true",
            },
        }, nil
    }

    // Create a new AWS session
    sess, err := session.NewSession(&aws.Config{
        Region: aws.String(region),
    })
    if err != nil {
        log.Printf("Failed to create AWS session: %v", err)
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusInternalServerError,
            Body:       fmt.Sprintf(`{"message": "Failed to create AWS session: %v"}`, err),
            Headers: map[string]string{
                "Access-Control-Allow-Origin":      "*",
                "Access-Control-Allow-Credentials": "true",
            },
        }, nil
    }

    svc := cognitoidentityprovider.New(sess)

    // Verify TOTP setup
    verifyInput := &cognitoidentityprovider.VerifySoftwareTokenInput{
        AccessToken: aws.String(setupRequest.AccessToken),
        UserCode:    aws.String(setupRequest.UserCode),
    }

    _, err = svc.VerifySoftwareToken(verifyInput)
    if err != nil {
        log.Printf("Error verifying software token: %v", err)
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusInternalServerError,
            Body:       fmt.Sprintf(`{"message": "Error verifying software token: %v"}`, err),
            Headers: map[string]string{
                "Access-Control-Allow-Origin":      "*",
                "Access-Control-Allow-Credentials": "true",
            },
        }, nil
    }

    response := SetupTOTPResponse{
        Message: "TOTP setup verified successfully",
    }
    responseBody, err := json.Marshal(response)
    if err != nil {
        log.Printf("Error marshalling response: %v", err)
        return events.APIGatewayProxyResponse{
            StatusCode: http.StatusInternalServerError,
            Body:       fmt.Sprintf(`{"message": "Error marshalling response: %v"}`, err),
            Headers: map[string]string{
                "Access-Control-Allow-Origin":      "*",
                "Access-Control-Allow-Credentials": "true",
            },
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: http.StatusOK,
        Body:       string(responseBody),
        Headers: map[string]string{
            "Access-Control-Allow-Origin":      "*",
            "Access-Control-Allow-Credentials": "true",
        },
    }, nil
}

func main() {
    lambda.Start(setupTOTPHandler)
}
