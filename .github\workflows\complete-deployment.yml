name: Complete Deployment Pipeline

on:
  push:
    branches:
      - main # Change this to your main branch name
    paths:
      - "lambda/dev/**"
      - "terraform/development/**"
      - "client/**"
      - ".github/workflows/complete-deployment.yml"
  workflow_dispatch: # Allow manual triggering

jobs:
  deploy-infrastructure:
    name: Deploy Infrastructure
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Create S3 bucket for Terraform state if it doesn't exist
        run: |
          aws s3api head-bucket --bucket dev-thealpinestudio-backend-tf 2>/dev/null || \
          aws s3 mb s3://dev-thealpinestudio-backend-tf --region us-west-2

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.5.0

      - name: Create terraform.tfvars file
        run: |
          cat > terraform/development/dev-env/terraform.tfvars << EOF
          infra_env = "development"
          access_key = "${{ secrets.AWS_ACCESS_KEY_ID }}"
          secret_key = "${{ secrets.AWS_SECRET_ACCESS_KEY }}"
          account_id = "${{ secrets.AWS_ACCOUNT_ID }}"
          zone_id = "${{ secrets.ROUTE53_ZONE_ID }}"
          domain_name = "test.thealpinestudio.com"
          hosting_bucket_name = "dev-thealpinestudio-hosting"
          list_products_function_name = "dev-list-products"
          api_gateway_id = "${{ secrets.DEV_API_GATEWAY_ID }}"
          api_gateway_account_id = "${{ secrets.AWS_ACCOUNT_ID }}"
          api_gateway_region = "us-west-2"
          stripe_secret_key = "${{ secrets.STRIPE_TEST_SECRET_KEY }}"
          stripe_endpoint_secret = "${{ secrets.STRIPE_TEST_ENDPOINT_SECRET }}"
          resend_api_key = "${{ secrets.RESEND_API_KEY }}"
          frontend_url = "https://test.thealpinestudio.com"
          dynamodb_orders_table = "dev-orders"
          EOF

      - name: Terraform Init
        run: |
          cd terraform/development/dev-env
          terraform init

      - name: Terraform Plan
        run: |
          cd terraform/development/dev-env
          terraform plan -var-file=terraform.tfvars -out=tfplan

      - name: Terraform Apply
        run: |
          cd terraform/development/dev-env
          terraform apply -auto-approve tfplan

  deploy-lambda:
    name: Deploy Lambda Functions
    runs-on: ubuntu-latest
    needs: deploy-infrastructure
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: "1.20"

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Build and Deploy Lambda Functions
        run: |
          # Create S3 bucket if it doesn't exist
          aws s3api head-bucket --bucket dev-thealpinestudio-lambda-functions-v1 2>/dev/null || \
          aws s3 mb s3://dev-thealpinestudio-lambda-functions-v1 --region us-west-2

          # Build and deploy ListProducts Lambda
          cd lambda/dev/ListProducts
          GOOS=linux GOARCH=amd64 go build -o bootstrap listProducts.go
          zip dev-list-products.zip bootstrap
          aws s3 cp dev-list-products.zip s3://dev-thealpinestudio-lambda-functions-v1/list-products.zip
          aws lambda update-function-code --function-name dev-list-products --s3-bucket dev-thealpinestudio-lambda-functions-v1 --s3-key list-products.zip --region us-west-2
          cd ../../..

          # Build and deploy CheckoutProducts Lambda
          cd lambda/dev/CheckoutProducts
          GOOS=linux GOARCH=amd64 go build -o bootstrap checkoutProducts.go
          zip dev-checkout-products.zip bootstrap
          aws s3 cp dev-checkout-products.zip s3://dev-thealpinestudio-lambda-functions-v1/checkout-products.zip
          aws lambda update-function-code --function-name dev-checkout-products --s3-bucket dev-thealpinestudio-lambda-functions-v1 --s3-key checkout-products.zip --region us-west-2
          cd ../../..

          # Build and deploy ContactEmail Lambda
          cd lambda/dev/ContactEmail
          GOOS=linux GOARCH=amd64 go build -o bootstrap contactEmail.go
          zip dev-contact-email.zip bootstrap
          aws s3 cp dev-contact-email.zip s3://dev-thealpinestudio-lambda-functions-v1/contact-email.zip
          aws lambda update-function-code --function-name dev-contact-email --s3-bucket dev-thealpinestudio-lambda-functions-v1 --s3-key contact-email.zip --region us-west-2
          cd ../../..

          # Build and deploy Webhooks Lambda
          cd lambda/dev/Webhooks
          GOOS=linux GOARCH=amd64 go build -o bootstrap emailWebhook.go
          zip dev-webhooks.zip bootstrap
          aws s3 cp dev-webhooks.zip s3://dev-thealpinestudio-lambda-functions-v1/webhooks.zip
          aws lambda update-function-code --function-name dev-webhooks --s3-bucket dev-thealpinestudio-lambda-functions-v1 --s3-key webhooks.zip --region us-west-2
          cd ../../..

  deploy-frontend:
    name: Deploy Frontend
    runs-on: ubuntu-latest
    needs: [deploy-infrastructure, deploy-lambda]
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"
          cache-dependency-path: client/package-lock.json

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Create .env file for frontend
        run: |
          cat > client/.env << EOF
          REACT_APP_STRIPE_LIVE_SECRET_KEY=${{ secrets.STRIPE_LIVE_SECRET_KEY }}
          REACT_APP_TEST_STRIPE_SECRET_KEY=${{ secrets.STRIPE_TEST_SECRET_KEY }}
          REACT_APP_CART_API_ROUTE=${{ secrets.REACT_APP_CART_API_ROUTE }}
          REACT_APP_PRODUCT_API_ROUTE=${{ secrets.REACT_APP_PRODUCT_API_ROUTE }}
          REACT_APP_CONTACT_API_ROUTE=${{ secrets.REACT_APP_CONTACT_API_ROUTE }}
          REACT_APP_S3_BUCKET=${{ secrets.REACT_APP_S3_BUCKET }}
          REACT_APP_PUBLIC_KEY=${{ secrets.REACT_APP_PUBLIC_KEY }}
          EOF

      - name: Install dependencies
        run: |
          cd client
          npm ci

      - name: Build frontend for test environment
        run: |
          cd client
          npm run build:dev

      - name: Deploy to S3
        run: |
          # Deploy to test environment
          aws s3 cp client/build/ s3://test.thealpinestudio.com-v1/ --recursive

      - name: Invalidate CloudFront cache
        run: |
          # Invalidate test CloudFront distribution
          aws cloudfront create-invalidation --distribution-id E333R3CHKXLYGZ --paths "/*"

  test-deployment:
    name: Test Deployment
    runs-on: ubuntu-latest
    needs: [deploy-frontend]
    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Test API Endpoints
        run: |
          echo "Testing dev-list-products endpoint..."
          API_URL=$(aws apigateway get-rest-apis --query "items[?name=='dev-lambda-api-gateway'].id" --output text)
          ENDPOINT="https://${API_URL}.execute-api.us-west-2.amazonaws.com/test/dev-list-products"
          curl -s "${ENDPOINT}" | grep -q "products" && echo "API Test: Success!" || echo "API Test: Failed!"

      - name: Test Frontend
        run: |
          echo "Testing frontend..."
          curl -s "https://test.thealpinestudio.com" | grep -q "<title>" && echo "Frontend Test: Success!" || echo "Frontend Test: Failed!"

      - name: Notify on completion
        run: |
          echo "Deployment completed successfully!"
