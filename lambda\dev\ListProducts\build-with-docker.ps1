# PowerShell script to build and deploy the ListProducts Lambda function using Docker

# Variables
$BUCKET_NAME = "dev-thealpinestudio-lambda-functions-v1"
$FILE_NAME = "list-products.zip"
$LAMBDA_FUNCTION_NAME = "dev-list-products"
$REGION = "us-west-2"
$PROFILE = "thealpinestudio"

Write-Host "=== Building and deploying $LAMBDA_FUNCTION_NAME using Docker ==="

# Build the Docker image
Write-Host "Building Docker image..."
docker build -t lambda-builder .

# Create output directory if it doesn't exist
if (-not (Test-Path -Path "output")) {
    New-Item -ItemType Directory -Path "output"
}

# Run the Docker container to build the Lambda function
Write-Host "Building Lambda function using Docker..."
docker run --rm -v "${PWD}/output:/output" lambda-builder

# Check if the build was successful
if (-not (Test-Path -Path "output/bootstrap")) {
    Write-Host "Build failed. The bootstrap file was not created."
    exit 1
} else {
    Write-Host "Build succeeded."
}

# Zip the executable
Write-Host "Creating zip file..."
Compress-Archive -Path "output/bootstrap" -DestinationPath $FILE_NAME -Force
if ($LASTEXITCODE -ne 0) {
    Write-Host "Zip failed."
    exit 1
} else {
    Write-Host "Zip succeeded."
}

# Upload the zipped file to S3
Write-Host "Uploading to S3..."
aws s3 cp $FILE_NAME "s3://$BUCKET_NAME/$FILE_NAME" --region $REGION --profile $PROFILE
if ($LASTEXITCODE -ne 0) {
    Write-Host "S3 upload failed."
    exit 1
} else {
    Write-Host "S3 upload succeeded."
}

# Update the Lambda function with the code from S3
Write-Host "Updating Lambda function..."
aws lambda update-function-code `
    --function-name $LAMBDA_FUNCTION_NAME `
    --s3-bucket $BUCKET_NAME `
    --s3-key $FILE_NAME `
    --region $REGION `
    --profile $PROFILE
if ($LASTEXITCODE -ne 0) {
    Write-Host "Lambda function update failed."
    exit 1
} else {
    Write-Host "Lambda function update succeeded."
}

# Test the Lambda function
Write-Host "Testing Lambda function..."
aws lambda invoke `
    --function-name $LAMBDA_FUNCTION_NAME `
    --payload '{}' `
    --region $REGION `
    --profile $PROFILE `
    response.json

# Display the response
Write-Host "Lambda function response:"
Get-Content response.json

Write-Host "=== Deployment complete ==="
