import React from 'react';
import { getCurrentEnvironment } from '../auth/aws-config-loader';

interface EnvironmentDebugProps {
  showInProduction?: boolean;
}

const EnvironmentDebug: React.FC<EnvironmentDebugProps> = ({ 
  showInProduction = false 
}) => {
  const environment = getCurrentEnvironment();
  const isProduction = environment === 'production';
  
  // Don't show in production unless explicitly requested
  if (isProduction && !showInProduction) {
    return null;
  }
  
  const style: React.CSSProperties = {
    position: 'fixed',
    bottom: '40px',
    right: '10px',
    padding: '5px 10px',
    borderRadius: '4px',
    fontSize: '12px',
    fontWeight: 'bold',
    zIndex: 9999,
    opacity: 0.8,
    backgroundColor: isProduction ? '#ff4d4f' : '#52c41a',
    color: 'white',
  };
  
  return (
    <div style={style}>
      ADMIN API: {environment.toUpperCase()}
    </div>
  );
};

export default EnvironmentDebug;
