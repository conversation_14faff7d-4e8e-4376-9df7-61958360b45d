package main

import (
    "context"
    "fmt"
    "os"
    "log"
    "encoding/json"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go-v2/aws"
    "github.com/aws/aws-sdk-go-v2/config"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb"
    "github.com/aws/aws-sdk-go-v2/service/dynamodb/types"
)

// HandleRequest is the lambda handler function for deleting a product
func AdminDeleteProductHandleRequest(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
    log.Printf("Received event: %+v", request)
    log.Printf("Received PathParameters: %+v", request.PathParameters)
    log.Printf("Received QueryStringParameters: %+v", request.QueryStringParameters)

    // Get the product ID from the path parameters
    productID, ok := request.PathParameters["id"]
    if !ok {
        return jsonErrorResponse(400, "Product ID is required")
    }

    // Get the title from the query string parameters
    title, ok := request.QueryStringParameters["title"]
    if !ok {
        return jsonErrorResponse(400, "Title is required")
    }

    // Get the subcategory from the query string parameters (optional)
    subcategory, subcatExists := request.QueryStringParameters["subcategory"]

    // Load the AWS configuration
    cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-west-2"))
    if err != nil {
        return jsonErrorResponse(500, fmt.Sprintf("Error loading configuration: %v", err))
    }

    // Create a DynamoDB client
    svc := dynamodb.NewFromConfig(cfg)

    // Prepare the key for deleting the product
    key := map[string]types.AttributeValue{
        "ID":    &types.AttributeValueMemberS{Value: productID},
        "Title": &types.AttributeValueMemberS{Value: title},
    }

    // If your primary key includes 'subcategory', include it in the key
    // Set this variable based on your table's primary key configuration
    subcategoryIsPartOfPrimaryKey := false // Set to true if subcategory is part of the primary key

    if subcategoryIsPartOfPrimaryKey {
        if !subcatExists || subcategory == "" {
            // If subcategory is required in the key but not provided, set to a default value like "None"
            subcategory = "None"
        }
        key["subcategory"] = &types.AttributeValueMemberS{Value: subcategory}
        log.Printf("Including subcategory in key: %s", subcategory)
    }

    input := &dynamodb.DeleteItemInput{
        TableName: aws.String(os.Getenv("TABLE_NAME")),
        Key:       key,
    }

    log.Printf("Deleting item with ID: %s, Title: %s from table: %s", productID, title, os.Getenv("TABLE_NAME"))

    // Delete the product from the DynamoDB table
    _, err = svc.DeleteItem(ctx, input)
    if err != nil {
        return jsonErrorResponse(500, fmt.Sprintf("Got error calling DeleteItem: %s", err))
    }

    // Return a success message
    return jsonResponse(200, map[string]string{
        "message": fmt.Sprintf("Product with ID %s and Title %s deleted successfully", productID, title),
    })
}

// Helper functions for responses
func jsonResponse(status int, data interface{}) (events.APIGatewayProxyResponse, error) {
    body, err := json.Marshal(data)
    if err != nil {
        return events.APIGatewayProxyResponse{
            StatusCode: 500,
            Body:       "Failed to create response",
            Headers:    corsHeaders(),
        }, nil
    }

    return events.APIGatewayProxyResponse{
        StatusCode: status,
        Body:       string(body),
        Headers:    corsHeaders(),
    }, nil
}

func jsonErrorResponse(status int, message string) (events.APIGatewayProxyResponse, error) {
    return jsonResponse(status, map[string]string{"message": message})
}

func corsHeaders() map[string]string {
    return map[string]string{
        "Access-Control-Allow-Origin":      "https://admin.thealpinestudio.com",
        "Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
        "Access-Control-Allow-Headers":     "Content-Type,Authorization",
        "Access-Control-Allow-Credentials": "true",
    }
}

func main() {
    lambda.Start(AdminDeleteProductHandleRequest)
}
