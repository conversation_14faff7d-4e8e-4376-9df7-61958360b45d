{
  "keys": [
    {
      "alg": "RS256",
      "e": "AQAB",
      "kid": "P4EQi9hoqV1xsjlKiXIsEYsHdd/zXu5WevuS1nJdsKg=",
      "kty": "RSA",
      "n": "nQUMjCtNDbk3r6CtaVcYV4x55ShWptmeCywqDFHbQnn7xJjIjsoQo6Wm5rCZzIFGQMTiiCj9uGIT8GjL9l_IPBxmgGoz7c2czIkW8JgYsFexw7DLxJL_FXDzXf-1IoURVkhvdGdamBWQ1xPv_9ewRZbJoqJj_lP3GZk9CwNy1YanKCUkpQ8zWlllQ5X2G90exMHYsJ1ufOvQlQcIt1Cnh8E8UFEv4he5tOagBeM8YhjoJh_8vFdlmGUtI_Uan2_s-jsNR96-F15zAHDs8_dYKQQBI7i66BuwCBGhNm47zfyi2QC14vfNzsN4pcdypt49eDpwgPzamROPgv2IHdEuIw",
      "use": "sig"
    },
    {
      "alg": "RS256",
      "e": "AQAB",
      "kid": "lXAoSd9ivjBpyVS4P4ALtpGaq+Ebu6hwVYQZ+Km10Vk=",
      "kty": "RSA",
      "n": "xJ1y61U4QKz0KZycIoBTmIUSPvjN8TYacz1VdUCkSs7eKfE2_T9_aE8crbzyS5FGCGtWIs4gHYDDsrFt1l-4ryzkruCLEfi3x8ZIhU39e-eGYIKqNKjr3SrzuaJ6WvNXA6_PFyQDPaucZj5Fap7c5jaYGNPkEGQxAZIelP7m9sUAxivdapU_7SEVy-p-pyqqLuquu64NmdlJhHzwpLo8iXfBg5ZA65uRaaAVfKDKwMOm8rf_p4E3gpZdEKyzumJV-MkkFcZhWaZri71Fv88oB5visfxGYEsNSy2CuVEg_I60x7_Je8QiEsywq3dgiCHulNHXePzxGfjwCrLgse8BMw",
      "use": "sig"
    }
  ]
}

{"keys":[{"alg":"RS256","e":"AQAB","kid":"P4EQi9hoqV1xsjlKiXIsEYsHdd/zXu5WevuS1nJdsKg=","kty":"RSA","n":"nQUMjCtNDbk3r6CtaVcYV4x55ShWptmeCywqDFHbQnn7xJjIjsoQo6Wm5rCZzIFGQMTiiCj9uGIT8GjL9l_IPBxmgGoz7c2czIkW8JgYsFexw7DLxJL_FXDzXf-1IoURVkhvdGdamBWQ1xPv_9ewRZbJoqJj_lP3GZk9CwNy1YanKCUkpQ8zWlllQ5X2G90exMHYsJ1ufOvQlQcIt1Cnh8E8UFEv4he5tOagBeM8YhjoJh_8vFdlmGUtI_Uan2_s-jsNR96-F15zAHDs8_dYKQQBI7i66BuwCBGhNm47zfyi2QC14vfNzsN4pcdypt49eDpwgPzamROPgv2IHdEuIw","use":"sig"},{"alg":"RS256","e":"AQAB","kid":"lXAoSd9ivjBpyVS4P4ALtpGaq+Ebu6hwVYQZ+Km10Vk=","kty":"RSA","n":"xJ1y61U4QKz0KZycIoBTmIUSPvjN8TYacz1VdUCkSs7eKfE2_T9_aE8crbzyS5FGCGtWIs4gHYDDsrFt1l-4ryzkruCLEfi3x8ZIhU39e-eGYIKqNKjr3SrzuaJ6WvNXA6_PFyQDPaucZj5Fap7c5jaYGNPkEGQxAZIelP7m9sUAxivdapU_7SEVy-p-pyqqLuquu64NmdlJhHzwpLo8iXfBg5ZA65uRaaAVfKDKwMOm8rf_p4E3gpZdEKyzumJV-MkkFcZhWaZri71Fv88oB5visfxGYEsNSy2CuVEg_I60x7_Je8QiEsywq3dgiCHulNHXePzxGfjwCrLgse8BMw","use":"sig"}]}


{
  "type": "TOKEN",
  "authorizationToken": "Bearer eyJraWQiOiJsWEFvU2Q5aXZqQnB5VlM0UDRBTHRwR2FxK0VidTZod1ZZUVorS20xMFZrPSIsImFsZyI6IlJTMjU2In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tgMZgNkHM2CIrU_GBoLAe6LO5KYly2oM6XRajpMO30d4XYbFYperxgr3Wz4kjzkp-R7k6hOJblwHbtaBVFYDLmYINlDGsxGyTLai0Hn7aCcoYb_fIe9ySjSJb1yOFAOvYgn9HIRKYZ6XhC2tru1RAyKBnHUguYrgR99lLRDObmFKGPQybDBhPS6CvoE8KowOnxZ0aFbENvvDRhipEnmp88jw_IeHeWB25hcjmIf72yk5_6xDdBTTdwRfulHxiIeD0kRuUxsnPvHu-38NKs7VJ36hRR59dBFkDOccKb9p9f_UCd7RtuwVr3-oR64ziClXAQeJUADgRtPMHSEXV7oghQ",
  "methodArn": "arn:aws:execute-api:us-west-2:410468036355:f2trvidc5b/*/POST/products"
}


{
  "body": "{\"username\": \"<EMAIL>\", \"session\": \"AYABeN-40ExFnOwJbujvIGB5os0AHQABAAdTZXJ2aWNlABBDb2duaXRvVXNlclBvb2xzAAEAB2F3cy1rbXMAS2Fybjphd3M6a21zOnVzLXdlc3QtMjowMTU3MzY3MjcxOTg6a2V5LzI5OTFhNGE5LTM5YTAtNDQ0Mi04MWU4LWRkYjY4NTllMTg2MQC4AQIBAHhPj7k9zU4nGXUQUvM0Ccwk42DS-fm3vKmH75ktTrktNQFzGI3EEtG9yY5vjIAGmiMwAAAAfjB8BgkqhkiG9w0BBwagbzBtAgEAMGgGCSqGSIb3DQEHATAeBglghkgBZQMEAS4wEQQMV6I0pdgjVFX0A4nyAgEQgDu3FHt31Ed9k_lh5LxYnARITJ0F9n1mKBk2QroAPhE3egTKsMS0rRzk1HOBJd76YffJtgdYGv4UgVrD5QIAAAAADAAAEAAAAAAAAAAAAAAAAAD3JsLPPsF9BujHjk8Ur8sb_____wAAAAEAAAAAAAAAAAAAAAEAAADxXKs201t51IO7AkgdrCN8ek3lKfxRBtnZS73HGRIchmGTGc15wD8QQwbqcCWe53IgZTMcZcUQbUBvFATYuR790Ccqjae5xb-A7OC5OhGfrk2ZM1VagKbQBzZ_3W8fv3-yccgJpRpbGV-eSYAKtK1je8QItu5XCxuTIkKSi9-sZWVkM-S3NU-jTMwygt8K0Mv45PbJ2-rdYnom629rH1MvT376v41nRMYeVHCzRWJRXkkpuTx1iZ7tgN3kw942y6rnKGvgeRIbuCNjj_gvrxRn_fIeyG5Aiik5ycI5dqk9ap2vXz5L_oZ2Fum7SZf5miJUb8HLIGwTTbO4lKptS-X1qPo\", \"mfa_code\": \"123456\"}"
}


  "body": "{\"message\":\"MFA verification successful\",\"token\":\"eyJraWQiOiJsWEFvU2Q5aXZqQnB5VlM0UDRBTHRwR2FxK0VidTZod1ZZUVorS20xMFZrPSIsImFsZyI6IlJTMjU2In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.gwqPPGwK2bVKegvvblQVtGqdG3pvp8PDdS0F3sLnuOgKSIo6IRXYWQm0PQMswtJWCm-konScZkAshyAWEX_u0lBhXhPaUF3wkkHL_BlzU4OfXg9RNdvF0vzFEQCmaBqPLFT0I1ox61wM1ZmOd1Z0o6uDaRDesn6DQLZ6Rodvd-yG9NcOFP4PHSSlmfneVSbU7F8Fe9Ttv3n3JpHnVfYRWhExNcvMyfjw-aGzyEKthLNAeuKiH-KinDdc-e3JtFW5XFEtigj5nZVU93NCXX5NPcydQmr5Jn_wLhAJNf3ZROWMltNVRnZinfVWGKqtkYDajbsUQRtR99D7ayLK3MRwpg\",\"id_token\":\"**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\",\"refresh_token\":\"eyJjdHkiOiJKV1QiLCJlbmMiOiJBMjU2R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.B9tCuGYNyvvKaeKEIf3hGn6-TIHy7CU7PSn_ZfsPWSydvgUAyTNIFkP2bCrWm-AW7OGBwtA1OfBXqu0c-rTTP1_BLUjgXfhYjKL-wxRyyiuX316N-yoOjXQW8lY_nY474Iu1H8Bu0lnxtKOsStwmlWT07YFU6lJtAItdsHPgypn4tLS2qNZyPLBwG9g4mh1oDTIiaJzOpk5y_sgq6E-925oVhdSp30tyDNIYOEMW__JW0e-7rJzwceDMmjRlAjVwPKkPR1uG5TLVW85bUMu2wAO6hsfCPoI-DEgJdpAq4x3Ph9uWYXSuh7Cjm035l6ZiIZQy60lT4EkfkPQ2SiwE_w.04yvUKlKhouJHtNp.DCRFs0JCRZQ2EGh7jAih9vfV3l-ZUGcDKom75cNxuHIX6d5wDA0DTNookpuhZGTx3diRafHmOBFlZk54t5AAV2IOmW9h6FFjrAMLTHkyJ9_koxdwZXtobVmYRGsx_owKd7vRQyl24o2qXF2vqP5cpz4aKiVEiZj6p3PrqQI8GfOBq6tbZLgbEPTPsSyCNNt37PD7AWB1yMgaPo8kjKfISba_qpra-KB-Z0yhaoxjQQ7lBhUFCk6iHj4weGvyKHkf7bOfqbqhwIKloPoOKnbT1XX_UDUKdEwtrpHAiZTKJz2pthUYGN0u18wsFBHrrtQoeWdH68rpR_a3yLgokRF5Fi7jCvl1BXtDuQfW1zdRaDeG1869tyNoEj32ZtD3lQma77ArTDA1cIHJMOuj_UlXS0kltgP2hQtbmgVTdDjoqFOg3bzuduilBPjTdeKhPFf9iuDjcIbKUVHTNF82HA8XXqFYtQDJbmBgaGQ3Gi0Y3a9_UoDVjMogGtmm7WSA9npLkN5q69l8KEbHXO1Q-hz1hHR5Yiae3i4mPnRU-_fgTh1WeukdtBe_e6LfFgSSmqy_D0ptoi_CaH1ZjEUyKp6aHzelDxV0bx9LjV3oiOpznoC9Hf6SQvyVQchd5LL4NaP-gxZFfFutZ6dOqMqhk5p66bHDlg4nwMd0kpslz6cdkf7RynGPEC1r3zZvuCVBTOJVpJAAgDBPFIeoQa4dbyxjYkqG60mS7SLA0p5ppeDO9hHIIak-5Bk4oPIRgq7mNwjJJXOAr6PrDGaW74FknzueSQxgljrqqNOmRnUXXgVhWblgsx0dZHAe9455We8ZZtoblsigx7sjUBDJyWt40llOV3Y6DiFjmGWi6chl5CI1A_puwBidjcDIf89rvmiEmvaxZM75bJyGJHMdDZb_cghfpQSckiJYrNrk48QWMspNubLT6F8Fwm0riouf5Ngi-5LlGdBeEGQgToyJrSs1C8Y1vJDoDy5Jcs_p5mZj_fzSZygtDwTSwXFue7QUNWErTjoOjKzrRnIkFyNGGXntzssEICPTXkb8ksfGnDAZ3LNxI8mJdyJ98BXN0juldxbmjb-ggDkxJnSVGECvp-lYK0FNGsrw1jdXOZCEKJqs0el_ryz3-Ct1TpBVewL2l61jRBWic4GYelj5OH36hBxrVqvbmCTV8SjdZ_astpjoZ1y_5PjlnZPW0bGnLka7E45OHOxwvjUmDWLYG2yfgxaXXfBqbiFnDJL-NFPaLHsEyEoJXkFLibR65CB88vY8BBEu3uyOIV1fD0x-b_aSW5xxPWuBiSi_QfK9bkYGkA.7eA_BzAo0LKAlG2HerU5Cg\"}"


  aws lambda invoke \
  --function-name token_authorizer \
  --payload '{"type":"TOKEN", "authorizationToken": "Bearer eyJraWQiOiJsWEFvU2Q5aXZqQnB5VlM0UDRBTHRwR2FxK0VidTZod1ZZUVorS20xMFZrPSIsImFsZyI6IlJTMjU2In0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.gwqPPGwK2bVKegvvblQVtGqdG3pvp8PDdS0F3sLnuOgKSIo6IRXYWQm0PQMswtJWCm-konScZkAshyAWEX_u0lBhXhPaUF3wkkHL_BlzU4OfXg9RNdvF0vzFEQCmaBqPLFT0I1ox61wM1ZmOd1Z0o6uDaRDesn6DQLZ6Rodvd-yG9NcOFP4PHSSlmfneVSbU7F8Fe9Ttv3n3JpHnVfYRWhExNcvMyfjw-aGzyEKthLNAeuKiH-KinDdc-e3JtFW5XFEtigj5nZVU93NCXX5NPcydQmr5Jn_wLhAJNf3ZROWMltNVRnZinfVWGKqtkYDajbsUQRtR99D7ayLK3MRwpg", "methodArn": "arn:aws:execute-api:us-west-2:410468036355/f2trvidc5b/GET/products"}' \
  response.json
