package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/joho/godotenv"
	"github.com/stripe/stripe-go/v78"
	"github.com/stripe/stripe-go/v78/paymentintent"
)

type CheckoutEvent struct {
	Items []Product `json:"items"`
}

type Product struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	Size     string `json:"size"`
	Currency string `json:"currency"`
	Quantity int64  `json:"quantity"`
	Price    int64  `json:"price"`
	ImageURL string `json:"imageUrl"` // Added ImageURL field if needed
}

type Response struct {
	ClientSecret string `json:"clientSecret"`
	Error        string `json:"error,omitempty"`
}

func init() {
	godotenv.Load()
}

func CheckoutProductHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	stripeKey := os.Getenv("STRIPE_SECRET_KEY")
	if len(stripeKey) < 5 {
		log.Printf("Stripe key is shorter than expected or not set: %s", stripeKey)
		return events.APIGatewayProxyResponse{StatusCode: 500}, fmt.Errorf("stripe key is not properly set")
	}

	stripe.Key = stripeKey
	log.Printf("Raw request body: %s", request.Body)

	var event CheckoutEvent
	err := json.Unmarshal([]byte(request.Body), &event)
	if err != nil {
		log.Printf("Error unmarshalling request body: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       fmt.Sprintf(`{"error":"Invalid request body: %v"}`, err),
			Headers:    defaultHeaders(),
		}, nil
	}

	log.Printf("Parsed event: %+v", event)

	if len(event.Items) == 0 {
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Body:       `{"error":"No products provided"}`,
			Headers:    defaultHeaders(),
		}, nil
	}

	// Calculate total amount
	var totalAmount int64
	currency := ""
	for _, product := range event.Items {
		totalAmount += product.Price * product.Quantity
		currency = product.Currency // Assuming all items have the same currency
	}

	params := &stripe.PaymentIntentParams{
		Amount:   stripe.Int64(totalAmount),
		Currency: stripe.String(currency),
	}

	pi, err := paymentintent.New(params)
	if err != nil {
		log.Printf("paymentintent.New: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Body:       fmt.Sprintf(`{"error":"%v"}`, err),
			Headers:    defaultHeaders(),
		}, nil
	}

	log.Printf("PaymentIntent created: %s", pi.ID)

	responseBody, _ := json.Marshal(Response{ClientSecret: pi.ClientSecret})
	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Body:       string(responseBody),
		Headers:    defaultHeaders(),
	}, nil
}

func defaultHeaders() map[string]string {
	return map[string]string{
		"Content-Type":                     "application/json",
		"Access-Control-Allow-Origin":      "*",
		"Access-Control-Allow-Methods":     "OPTIONS,POST,GET",
		"Access-Control-Allow-Headers":     "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
		"Access-Control-Allow-Credentials": "true",
	}
}

func main() {
	lambda.Start(CheckoutProductHandler)
}
