#!/bin/bash
# Script to start the development server with production environment variables

# Set environment variables for production
export NODE_ENV=production
export REACT_APP_ENVIRONMENT=production
export REACT_APP_CART_API_ROUTE=https://api.thealpinestudio.com/test-checkout-products
export REACT_APP_PRODUCT_API_ROUTE=https://api.thealpinestudio.com/test-list-products
export REACT_APP_CONTACT_API_ROUTE=https://api.thealpinestudio.com/test-contact-email
export REACT_APP_WEBHOOKS_API_ROUTE=https://api.thealpinestudio.com/test-webhooks
export REACT_APP_STRIPE_PUBLIC_KEY=pk_live_51NirT5H1KC3YYzQbtb2YFUchsLWDDLZ42Wx8qj81U89hFgcFEP540ytEyuLChzCQt1U5zuxn1rsBMrrJRETiTBvF000sAqnpMS
export REACT_APP_S3_BUCKET=thealpinestudio.com

# Navigate to client directory
cd client

# Start the development server with production variables
npm start
