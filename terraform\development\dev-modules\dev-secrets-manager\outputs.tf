# Outputs for the secrets manager module

output "stripe_publishable_key_arn" {
  description = "ARN of the Stripe publishable key secret"
  value       = aws_secretsmanager_secret.stripe_publishable_key.arn
}

output "stripe_secret_key_arn" {
  description = "ARN of the Stripe secret key secret"
  value       = aws_secretsmanager_secret.stripe_secret_key.arn
}

output "stripe_endpoint_secret_arn" {
  description = "ARN of the Stripe endpoint secret"
  value       = aws_secretsmanager_secret.stripe_endpoint_secret.arn
}

output "resend_api_key_arn" {
  description = "ARN of the Resend API key secret"
  value       = aws_secretsmanager_secret.resend_api_key.arn
}

output "frontend_api_routes_arn" {
  description = "ARN of the frontend API routes secret"
  value       = aws_secretsmanager_secret.frontend_api_routes.arn
}

output "frontend_api_routes_name" {
  description = "Name of the frontend API routes secret"
  value       = aws_secretsmanager_secret.frontend_api_routes.name
}
