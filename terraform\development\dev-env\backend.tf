

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.45.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.3.0"
    }
  }

  # Temporarily using local state until S3 permissions are resolved
  # backend "s3" {
  #   bucket = "dev-thealpinestudio-backend-tf"
  #   key    = "./.terraform/terraform.tfstate"
  #   region = "us-west-2"
  # }
}
