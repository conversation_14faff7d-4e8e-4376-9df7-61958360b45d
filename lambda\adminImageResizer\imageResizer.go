package main

import (
    "bytes"
    "context"
    "fmt"
    "log"
    "os"
    "path/filepath"
    "strings"

    "github.com/aws/aws-lambda-go/events"
    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/dynamodb"
    "github.com/aws/aws-sdk-go/service/s3"
    "github.com/disintegration/imaging"
)

type Product struct {
    ID    string `json:"ID"`
    Title string `json:"Title"`
    // Add other fields if necessary
}

func resizeImageHandler(ctx context.Context, s3Event events.S3Event) error {
    log.Println("Lambda function invoked.")

    // Initialize AWS session
    sess, err := session.NewSession(&aws.Config{
        Region: aws.String(os.Getenv("AWS_REGION")),
    })
    if err != nil {
        log.Printf("Failed to create AWS session: %v", err)
        return fmt.Errorf("failed to create AWS session: %v", err)
    }

    s3Client := s3.New(sess)
    dynamoClient := dynamodb.New(sess)

    // Retrieve environment variables
    imageBucketName := os.Getenv("IMAGE_BUCKET_NAME")
    tableName := os.Getenv("TABLE_NAME")

    if imageBucketName == "" || tableName == "" {
        log.Println("Environment variables IMAGE_BUCKET_NAME and TABLE_NAME must be set.")
        return fmt.Errorf("environment variables IMAGE_BUCKET_NAME and TABLE_NAME must be set")
    }

    for _, record := range s3Event.Records {
        bucket := record.S3.Bucket.Name
        key := record.S3.Object.Key

        log.Printf("Processing S3 event for bucket: %s, key: %s", bucket, key)

        if !strings.HasPrefix(key, "originals/") {
            log.Printf("Ignoring object with key %s as it does not match the 'originals/' prefix.", key)
            continue
        }

        // Download the original image
        originalImageBytes, contentType, err := downloadImage(s3Client, bucket, key)
        if err != nil {
            log.Printf("Failed to download image: %v", err)
            continue // Continue processing other records
        }
        log.Printf("Downloaded image size: %d bytes, Content-Type: %s", len(originalImageBytes), contentType)

        // Decode the image using imaging.Decode
        img, err := imaging.Decode(bytes.NewReader(originalImageBytes))
        if err != nil {
            log.Printf("Failed to decode image: %v", err)
            continue // Continue processing other records
        }
        log.Println("Image decoded successfully.")

        // Define the desired width while maintaining aspect ratio
        desiredWidth := 800
        resizedImg := imaging.Resize(img, desiredWidth, 0, imaging.Lanczos)
        log.Printf("Image resized to width: %dpx", desiredWidth)

        // Encode the resized image back to JPEG
        var buf bytes.Buffer
        err = imaging.Encode(&buf, resizedImg, imaging.JPEG, imaging.JPEGQuality(80))
        if err != nil {
            log.Printf("Failed to encode resized JPEG image: %v", err)
            continue // Continue processing other records
        }
        log.Printf("Resized image encoded successfully, size: %d bytes", buf.Len())

        // Construct the resized image key
        filename := filepath.Base(key)
        productID := strings.TrimSuffix(filename, filepath.Ext(filename))
        resizeKey := fmt.Sprintf("resized/%s.jpg", productID)
        log.Printf("Resized image key: %s", resizeKey)

        // Upload the resized image
        err = uploadImage(s3Client, imageBucketName, resizeKey, buf.Bytes(), "image/jpeg")
        if err != nil {
            log.Printf("Failed to upload resized image to S3: %v", err)
            continue // Continue processing other records
        }
        log.Printf("Resized image uploaded successfully: %s", resizeKey)

        // Retrieve the existing product to get the Title
        title, err := getProductTitle(dynamoClient, tableName, productID)
        if err != nil {
            log.Printf("Failed to retrieve product title for ID %s: %v", productID, err)
            continue // Continue processing other records
        }
        log.Printf("Retrieved product title: %s", title)

        // Update DynamoDB with the new ImageUrl
        resizedImageUrl := fmt.Sprintf("https://%s.s3.amazonaws.com/%s", imageBucketName, resizeKey)
        log.Printf("Updating DynamoDB for product ID: %s with ImageUrl: %s", productID, resizedImageUrl)
        err = updateProductImageUrl(dynamoClient, tableName, productID, title, resizedImageUrl)
        if err != nil {
            log.Printf("Failed to update DynamoDB with new ImageUrl: %v", err)
            continue // Continue processing other records
        }
        log.Printf("DynamoDB updated successfully for product ID: %s", productID)
    }

    log.Println("Lambda function completed successfully.")
    return nil
}

func downloadImage(s3Client *s3.S3, bucket, key string) ([]byte, string, error) {
    output, err := s3Client.GetObject(&s3.GetObjectInput{
        Bucket: aws.String(bucket),
        Key:    aws.String(key),
    })
    if err != nil {
        return nil, "", fmt.Errorf("failed to get object from S3: %v", err)
    }
    defer output.Body.Close()

    buf := new(bytes.Buffer)
    _, err = buf.ReadFrom(output.Body)
    if err != nil {
        return nil, "", fmt.Errorf("failed to read object data: %v", err)
    }

    contentType := aws.StringValue(output.ContentType)
    return buf.Bytes(), contentType, nil
}

func uploadImage(s3Client *s3.S3, bucket, key string, data []byte, contentType string) error {
    _, err := s3Client.PutObject(&s3.PutObjectInput{
        Bucket:      aws.String(bucket),
        Key:         aws.String(key),
        Body:        bytes.NewReader(data),
        ContentType: aws.String(contentType),
    })
    if err != nil {
        return fmt.Errorf("failed to upload image to S3: %v", err)
    }
    return nil
}

func getProductTitle(dynamoClient *dynamodb.DynamoDB, tableName, productID string) (string, error) {
    // Perform a Query operation to retrieve the Title based on ID
    input := &dynamodb.QueryInput{
        TableName:              aws.String(tableName),
        KeyConditionExpression: aws.String("ID = :id"),
        ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
            ":id": {S: aws.String(productID)},
        },
        ProjectionExpression: aws.String("Title"),
        Limit:                aws.Int64(1),
    }

    result, err := dynamoClient.Query(input)
    if err != nil {
        return "", fmt.Errorf("failed to query DynamoDB: %v", err)
    }

    if len(result.Items) == 0 {
        return "", fmt.Errorf("no product found with ID %s", productID)
    }

    titleAttr, ok := result.Items[0]["Title"]
    if !ok || titleAttr.S == nil {
        return "", fmt.Errorf("Title attribute not found for product ID %s", productID)
    }

    return *titleAttr.S, nil
}

func updateProductImageUrl(dynamoClient *dynamodb.DynamoDB, tableName, productID, title, imageUrl string) error {
    input := &dynamodb.UpdateItemInput{
        TableName: aws.String(tableName),
        Key: map[string]*dynamodb.AttributeValue{
            "ID":    {S: aws.String(productID)},
            "Title": {S: aws.String(title)},
        },
        UpdateExpression: aws.String("SET ImageUrl = :u"),
        ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
            ":u": {S: aws.String(imageUrl)},
        },
        ReturnValues: aws.String("NONE"),
    }

    _, err := dynamoClient.UpdateItem(input)
    if err != nil {
        return fmt.Errorf("failed to update item in DynamoDB: %v", err)
    }

    return nil
}

func main() {
    lambda.Start(resizeImageHandler)
}
