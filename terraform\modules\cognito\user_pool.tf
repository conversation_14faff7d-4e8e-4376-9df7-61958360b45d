

locals {
  provider_name = "https://cognito-idp.${data.aws_region.current.name}.amazonaws.com/${aws_cognito_user_pool.user_pool.id}"
}

data "aws_region" "current" {}



resource "aws_cognito_user_pool" "user_pool" {
  name = var.user_pool_name
  username_configuration {
    case_sensitive = true
  }
}


resource "aws_cognito_user_pool_client" "user_pool_client" {
  name = var.user_pool_client_name
  user_pool_id = aws_cognito_user_pool.user_pool.id
  prevent_user_existence_errors = "ENABLED"
  explicit_auth_flows = [
    "ALLOW_USER_PASSWORD_AUTH", "ALLOW_REFRESH_TOKEN_AUTH", "ALLOW_USER_SRP_AUTH"
  ]
}

resource "aws_cognito_identity_pool" "identity_pool" {
  depends_on = [aws_cognito_user_pool.user_pool]
  identity_pool_name = var.identity_pool_name
  allow_unauthenticated_identities = true

  cognito_identity_providers {
    client_id = aws_cognito_user_pool_client.user_pool_client.id
    provider_name = "cognito-idp.us-east-1.amazonaws.com/us-west-2_P8OmH8DJU"
  }
}
resource "aws_iam_role" "unauth_role" {
  name = "Cognito_DefaultUnauthenticatedRole"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRoleWithWebIdentity",
        Effect = "Allow",
        Principal = {
          Federated = "cognito-identity.amazonaws.com"
        },
        Condition = {
          "StringEquals": {
            "cognito-identity.amazonaws.com:aud": aws_cognito_identity_pool.identity_pool.id
          },
          "ForAnyValue:StringLike": {
            "cognito-identity.amazonaws.com:amr": "unauthenticated"
          }
        }
      }
    ]
  })
}


resource "aws_cognito_identity_pool_roles_attachment" "identity_pool_roles" {
  identity_pool_id = aws_cognito_identity_pool.identity_pool.id

  roles = {
    "unauthenticated" = aws_iam_role.unauth_role.arn
  }
}

resource "aws_iam_role_policy" "api_gateway_access" {
  name = "APIGatewayAccessPolicy"
  role = aws_iam_role.unauth_role.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action   = ["execute-api:Invoke"],
        Effect   = "Allow",
        Resource = "arn:aws:execute-api:${var.api_gateway_region}:${var.api_gateway_account_id}:${var.api_gateway_id}/*"
      }
    ]
  })
}
