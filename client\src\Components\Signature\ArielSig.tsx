import React, { useEffect, useRef } from "react";
import anime from "animejs";

const ArielSig = () => {
  const pathRef = useRef<SVGPathElement |null>(null);  // Create a ref to hold the SVG path

  useEffect(() => {
    if (pathRef.current) {
        const pathLength = pathRef.current.getTotalLength();
        pathRef.current.style.strokeDasharray = `${pathLength}px`;
        pathRef.current.style.strokeDashoffset = `${pathLength}px`;
        
        anime({
            targets: pathRef.current,
            strokeDashoffset: [anime.setDashoffset, 0],
            duration: 3000,
            directionn: 'alternate',
            easing: 'easeInOutSine'
        });
    }
}, []);


  return (
    <div>
      <svg
        width="108mm"
        height="62mm"
        viewBox="0 0 68.312889 42.491264"
        version="1.1"
        id="svg1"
        xmlSpace="preserve"
        inkscape:export-filename="Ariel_sig"
        inkscape:export-xdpi="96.036186"
        inkscape:export-ydpi="96.036186"
        xmlns="http://www.w3.org/2000/svg"
      >
        <defs id="defs1" />
        <g id="layer1" transform="translate(-68.88083,-117.52839)">
          <path
          ref={pathRef}
          className="path"
          stroke="black"
          fill="none"
          strokeWidth={.3}
          strokeDasharray="1000"
          strokeDashoffset="1000"
        
            d="m 131.36815,159.96519 c -0.20214,-0.0535 -0.23063,-0.20017 -0.1,-0.51453 0.0976,-0.23482 0.15702,-0.27516 0.40516,-0.27516 0.28742,0 0.29026,0.004 0.24654,0.3175 -0.0776,0.55679 -0.11414,0.58809 -0.55175,0.47219 z m -62.018881,-1.08635 c -0.363628,-0.1529 -0.493353,-0.51442 -0.464571,-1.29469 0.0198,-0.53685 0.606081,-1.08713 0.606081,-0.56887 0,0.11575 -0.04521,0.3795 -0.100473,0.58611 -0.08045,0.30078 -0.07838,0.40229 0.01037,0.50922 0.204444,0.24634 0.426238,0.17846 1.27544,-0.39034 0.527135,-0.35308 1.028426,-0.86585 2.794,-2.85795 0.256116,-0.28898 0.694266,-0.76544 0.973666,-1.0588 0.640481,-0.67249 1.662414,-1.84711 2.32412,-2.67136 0.280378,-0.34925 0.583053,-0.71584 0.672612,-0.81465 0.08956,-0.0988 0.303599,-0.36552 0.475645,-0.59267 0.172046,-0.22716 0.527944,-0.6414 0.790884,-0.92055 0.482871,-0.51263 0.610275,-0.67254 1.147153,-1.43979 0.162923,-0.23284 0.414749,-0.55208 0.559613,-0.70943 0.144865,-0.15735 0.326896,-0.38595 0.404515,-0.508 0.129132,-0.20305 0.818051,-1.11854 1.084807,-1.44157 0.191853,-0.23233 0.959836,-1.28757 1.522036,-2.09134 0.296605,-0.42405 0.597407,-0.81924 0.668448,-0.8782 0.07104,-0.059 0.129167,-0.15378 0.129167,-0.21071 0,-0.0569 0.0762,-0.17247 0.169333,-0.25675 0.09313,-0.0843 0.169333,-0.20449 0.169333,-0.26713 0,-0.0626 0.0321,-0.11387 0.07134,-0.11387 0.03923,0 0.143147,-0.13335 0.23092,-0.29634 0.08777,-0.16298 0.187066,-0.29633 0.220652,-0.29633 0.03359,0 0.258779,-0.29528 0.500427,-0.65617 0.241648,-0.36089 0.467068,-0.67521 0.500935,-0.6985 0.03387,-0.0233 0.08643,-0.12207 0.116803,-0.21953 0.03038,-0.0975 0.206961,-0.38056 0.392411,-0.62912 0.185451,-0.24856 0.337184,-0.48626 0.337184,-0.52822 0,-0.042 0.13335,-0.23903 0.296333,-0.43793 0.162984,-0.1989 0.296334,-0.39058 0.296334,-0.42596 0,-0.0354 0.09822,-0.18386 0.218278,-0.32995 0.29224,-0.35562 1.240559,-1.80793 1.572401,-2.40806 0.146658,-0.26522 0.293917,-0.50128 0.327241,-0.52456 0.03332,-0.0233 0.09034,-0.13758 0.126704,-0.254 0.07214,-0.23095 0.38292,-0.74922 0.563069,-0.93898 0.182233,-0.19196 0.57894,-0.79859 0.579337,-0.8859 2.01e-4,-0.0441 0.144047,-0.27062 0.319659,-0.50345 0.175612,-0.23284 0.423703,-0.59479 0.551315,-0.80434 0.324109,-0.53222 0.403436,-0.65592 0.716162,-1.11676 0.151342,-0.22303 0.275167,-0.43017 0.275167,-0.46032 0,-0.0301 0.17145,-0.31506 0.381,-0.63313 0.20955,-0.31808 0.381046,-0.60813 0.381103,-0.64456 5.7e-5,-0.0364 0.09903,-0.18053 0.219948,-0.32023 0.120914,-0.1397 0.310376,-0.42545 0.421026,-0.635 0.11065,-0.20955 0.373107,-0.6477 0.583238,-0.97366 0.210131,-0.32597 0.479366,-0.80093 0.5983,-1.05546 0.118934,-0.25453 0.243382,-0.48313 0.276552,-0.508 0.112138,-0.0841 0.516589,-0.75313 0.948597,-1.56921 0.123254,-0.23283 0.33448,-0.59478 0.46939,-0.80433 0.13491,-0.20955 0.627467,-1.143 1.094567,-2.07434 0.69074,-1.37723 0.88484,-1.69838 1.03978,-1.72039 0.16936,-0.0241 0.19022,0.009 0.18803,0.29634 -0.001,0.17786 -0.0799,0.59009 -0.17458,0.91605 -0.0947,0.32597 -0.20345,0.72602 -0.24175,0.889 -0.0383,0.16299 -0.13696,0.48684 -0.21927,0.71967 -0.51142,1.44678 -0.74715,2.07005 -0.89164,2.3575 -0.09,0.17903 -0.16362,0.37025 -0.16362,0.42495 0,0.0547 -0.167168,0.44162 -0.371477,0.85983 -0.327439,0.67025 -0.675756,1.42307 -0.900257,1.94572 -0.04,0.0931 -0.469891,0.98848 -0.955302,1.98967 -0.485412,1.00118 -1.024707,2.12513 -1.198434,2.49766 -0.173727,0.37254 -0.431305,0.92499 -0.572395,1.22767 -0.141091,0.30268 -0.334146,0.70273 -0.429012,0.889 -0.09487,0.18627 -0.232942,0.47202 -0.306834,0.635 -0.38458,0.84827 -4.944484,9.85492 -5.422446,10.71033 -0.955332,1.70977 -1.358514,2.58614 -1.358514,2.95294 0,0.32696 -0.03602,0.40552 -0.258682,0.56406 -0.319145,0.22726 -0.537259,0.56913 -1.263592,1.98057 -0.313158,0.60854 -0.790691,1.52553 -1.061185,2.03777 -0.623821,1.18133 -0.688661,1.30862 -0.797816,1.56633 -0.09159,0.21625 -0.129084,0.28038 -0.534558,0.91441 -0.128058,0.20024 -0.232833,0.40186 -0.232833,0.44803 0,0.0462 -0.119834,0.33954 -0.266298,0.65192 -0.528866,1.12799 -0.749702,1.64411 -0.749702,1.75214 0,0.0605 -0.05964,0.13294 -0.132537,0.16091 -0.09577,0.0367 -0.12168,0.01 -0.0934,-0.0968 0.02153,-0.0812 0.08785,-0.39528 0.147391,-0.69796 0.05954,-0.30269 0.177769,-0.67532 0.262732,-0.82808 0.08496,-0.15276 0.154479,-0.34608 0.154479,-0.4296 0,-0.0835 0.146285,-0.43479 0.325077,-0.78059 0.178792,-0.3458 0.391255,-0.82831 0.47214,-1.07224 0.08088,-0.24392 0.179838,-0.46376 0.219895,-0.48851 0.08541,-0.0528 0.337555,-0.54054 0.337555,-0.65297 0,-0.0425 0.09005,-0.19859 0.200115,-0.34683 0.25554,-0.34416 0.730784,-1.22263 0.732629,-1.35423 7.77e-4,-0.0553 0.172227,-0.38463 0.381,-0.73176 0.208774,-0.34712 0.379589,-0.66617 0.379589,-0.70898 0,-0.0448 -0.206651,-0.0541 -0.486833,-0.022 -0.267759,0.0307 -0.723922,0.0779 -1.013697,0.10487 -0.289775,0.0269 -0.561436,0.0922 -0.603691,0.14511 -0.04225,0.0529 -0.141363,0.41865 -0.220241,0.81285 -0.162001,0.80963 -0.178354,0.8496 -0.347535,0.8496 -0.269304,0 -0.06638,-1.81527 0.24266,-2.17069 0.130951,-0.15061 0.261308,-0.19997 0.528049,-0.19997 0.845235,0 2.062033,-0.4099 2.501292,-0.8426 0.209372,-0.20625 0.421928,-0.56419 1.147584,-1.93254 0.17971,-0.33887 0.431026,-0.79607 0.558481,-1.016 0.127455,-0.21992 0.276952,-0.51416 0.332216,-0.65386 0.102258,-0.2585 1.17405,-2.36212 2.372565,-4.65667 0.672313,-1.28714 0.919521,-1.79347 1.32279,-2.70933 0.123024,-0.2794 0.386813,-0.8128 0.586198,-1.18534 0.381658,-0.71309 0.671904,-1.30284 1.020894,-2.07433 0.265709,-0.58739 0.210565,-0.47483 1.069103,-2.18222 0.399757,-0.795 0.726749,-1.47696 0.726649,-1.51545 -1e-4,-0.0385 0.281078,-0.6415 0.62484,-1.34 1.334161,-2.71092 1.661342,-3.39989 1.661342,-3.49844 0,-0.0562 0.03616,-0.16835 0.08036,-0.24921 0.147191,-0.26929 0.581695,-1.24164 0.765623,-1.71335 0.09987,-0.25611 0.230275,-0.54186 0.289799,-0.635 0.152664,-0.23885 0.408514,-1.03978 0.369524,-1.15675 -0.0307,-0.0921 -0.997305,1.28013 -0.997305,1.41585 0,0.035 -0.08667,0.18148 -0.192608,0.32558 -0.105935,0.14409 -0.302437,0.47154 -0.436671,0.72766 -0.134235,0.25611 -0.3098,0.56091 -0.390145,0.67733 -0.08035,0.11642 -0.226744,0.35454 -0.325329,0.52917 -0.449119,0.79552 -0.538811,0.94982 -0.862897,1.48448 -0.189593,0.31277 -0.499091,0.81633 -0.687774,1.11902 -0.188682,0.30268 -0.418961,0.68368 -0.51173,0.84666 -0.09277,0.16299 -0.249647,0.41044 -0.348617,0.5499 -0.09897,0.13946 -0.27961,0.44565 -0.401421,0.68043 -0.310323,0.59812 -0.44843,0.83727 -0.511861,0.88634 -0.09895,0.0765 -0.91821,1.38878 -1.113063,1.78281 -0.104933,0.2122 -0.332785,0.57926 -0.506336,0.81568 -0.173551,0.23643 -0.315547,0.45259 -0.315547,0.48038 0,0.0278 -0.13335,0.25897 -0.296334,0.51375 -0.162983,0.25479 -0.296333,0.50318 -0.296333,0.55198 0,0.0488 -0.03108,0.0887 -0.06908,0.0887 -0.03799,0 -0.161817,0.15906 -0.275167,0.35345 -0.355971,0.6105 -1.084386,1.73006 -2.289362,3.51869 -0.32105,0.47656 -0.583728,0.90201 -0.583728,0.94544 0,0.0434 -0.02864,0.0917 -0.06365,0.1072 -0.03501,0.0155 -0.399902,0.53508 -0.810877,1.15456 -0.410976,0.61949 -0.850734,1.24814 -0.977239,1.397 -0.204379,0.2405 -0.356861,0.45685 -0.644876,0.91497 -0.208238,0.33123 -0.621497,0.91908 -0.975551,1.38769 -0.352068,0.46599 -0.483144,0.64994 -0.845809,1.18697 -0.230835,0.34181 -1.351024,1.80599 -2.08853,2.72988 -0.399163,0.50003 -0.81652,1.02345 -0.92746,1.16315 -0.413471,0.52066 -2.291126,2.73597 -2.948948,3.47926 -0.374767,0.42345 -0.942156,1.07115 -1.260865,1.43933 -0.318708,0.36818 -0.932193,1.05041 -1.363301,1.51608 -0.431107,0.46567 -0.982932,1.07527 -1.226277,1.35467 -0.243344,0.2794 -0.620459,0.65383 -0.838032,0.83207 -0.217573,0.17824 -0.471787,0.38853 -0.56492,0.4673 -0.588015,0.49734 -1.2408,0.72374 -1.623177,0.56297 z m 54.031841,-0.3101 c -1.75106,-0.15182 -3.11825,-0.44892 -4.0889,-0.88854 -0.35884,-0.16253 -0.76681,-0.34708 -0.9066,-0.41011 -1.36158,-0.61396 -2.05663,-1.08651 -2.6727,-1.81714 -0.19762,-0.23436 -0.41098,-0.42612 -0.47412,-0.42612 -0.10661,0 -0.40439,0.57201 -0.66115,1.27 -0.06,0.16298 -0.20882,0.51316 -0.3308,0.77817 -0.12198,0.26501 -0.25886,0.57933 -0.30416,0.6985 -0.0871,0.22897 -0.34057,0.3007 -0.34057,0.0964 0,-0.0662 0.13546,-0.43764 0.30104,-0.8255 0.53154,-1.24518 1.44641,-3.75574 2.10818,-5.78521 0.21258,-0.65193 0.42422,-1.25149 0.47031,-1.33235 0.0461,-0.0809 0.0838,-0.1972 0.0838,-0.25854 0,-0.0613 0.0901,-0.29998 0.20026,-0.53032 0.32223,-0.67388 0.4751,-1.02689 0.60892,-1.40618 0.0691,-0.19577 0.25573,-0.62519 0.41481,-0.95428 0.3628,-0.75053 0.55401,-1.19553 0.55401,-1.28932 0,-0.04 0.0986,-0.17904 0.21904,-0.30896 0.17446,-0.18815 0.24338,-0.21601 0.33866,-0.13693 0.18146,0.15059 0.1501,0.29877 -0.23029,1.0881 -0.61937,1.28524 -1.04241,2.27694 -1.14016,2.6728 -0.0344,0.13934 -0.22757,0.63062 -0.42924,1.09174 -0.20167,0.46112 -0.36668,0.87335 -0.36668,0.91607 0,0.0427 -0.0923,0.27532 -0.20521,0.51692 -0.15205,0.32546 -0.64145,1.69954 -0.64145,1.801 0,0.0762 0.44915,-0.10695 0.55839,-0.22765 0.0798,-0.0882 0.18102,-0.16043 0.22483,-0.16043 0.0438,0 0.1937,-0.0812 0.33311,-0.18049 0.41847,-0.29797 3.59333,-1.93618 3.75234,-1.93618 0.055,0 0.93315,-0.39231 1.60833,-0.71851 0.72255,-0.34908 1.22328,-0.57037 1.82715,-0.80748 1.59511,-0.62632 4.93754,-2.28734 7.12635,-3.54143 0.17462,-0.10005 0.46037,-0.26241 0.635,-0.3608 1.80741,-1.01836 3.52302,-2.42885 4.1811,-3.43748 0.97432,-1.49334 0.85184,-2.91268 -0.37834,-4.38437 -0.29789,-0.35638 -1.42087,-1.31259 -1.54151,-1.31259 -0.0302,0 -0.15608,-0.0701 -0.27968,-0.15587 -0.24607,-0.17065 -1.42899,-0.676 -1.83341,-0.78324 -0.1397,-0.037 -0.4064,-0.1085 -0.59266,-0.1588 -1.20183,-0.32455 -2.2844,-0.4292 -4.43869,-0.42909 -1.19092,7e-5 -2.56536,0.0416 -3.05431,0.0922 -0.48895,0.0506 -1.1938,0.12335 -1.56634,0.16161 -0.37253,0.0383 -0.81068,0.0931 -0.97366,0.1218 -0.16299,0.0287 -0.52494,0.0917 -0.80434,0.14003 -0.62117,0.10737 -1.27475,0.23421 -1.94733,0.37793 -0.2794,0.0597 -0.7366,0.15659 -1.016,0.2153 -0.2794,0.0587 -0.54419,0.13871 -0.58843,0.17778 -0.0442,0.0391 -0.16627,0.071 -0.27119,0.071 -0.10491,0 -0.27826,0.0341 -0.38523,0.0759 -0.10696,0.0417 -0.34688,0.11745 -0.53315,0.16829 -1.19768,0.32691 -1.97103,0.59392 -3.175,1.09622 -1.19165,0.49716 -1.51996,0.67592 -2.01083,1.09485 -0.40351,0.34438 -0.44445,0.41159 -0.44398,0.72891 2.9e-4,0.19225 0.0558,0.4555 0.12334,0.585 0.13092,0.25099 0.10082,0.43233 -0.11297,0.68073 -0.073,0.0848 -0.13494,0.21816 -0.13768,0.29634 -0.0147,0.41991 -0.38709,2.51158 -0.55285,3.10548 -0.039,0.1397 -0.11591,0.42545 -0.17094,0.635 -0.055,0.20955 -0.1321,0.4572 -0.17127,0.55033 -0.0392,0.0931 -0.17168,0.47414 -0.29446,0.84667 -0.23428,0.71078 -0.57665,1.43565 -1.07973,2.286 -0.1653,0.2794 -0.40819,0.71158 -0.53975,0.96039 -0.26781,0.50648 -1.72468,2.04072 -2.30737,2.42992 -0.23064,0.15405 -0.44933,0.39692 -0.5685,0.63136 -0.20529,0.40385 -0.62301,1.65879 -0.62301,1.87168 0,0.0696 -0.0375,0.16397 -0.0834,0.20982 -0.12749,0.12749 -0.44731,2.14899 -0.39807,2.5161 0.0513,0.38272 -0.14079,0.5301 -0.34329,0.26332 -0.0979,-0.12902 -0.11209,-0.3426 -0.0697,-1.05092 0.0528,-0.88258 0.16877,-1.4945 0.4789,-2.52776 0.2208,-0.73561 0.21244,-0.85891 -0.0583,-0.85891 -0.12087,0 -0.3209,0.0705 -0.4445,0.15678 -0.42931,0.29948 -1.58365,0.84875 -2.32698,1.10725 -0.644977,0.22429 -0.865767,0.25993 -1.608667,0.25965 -0.96753,-3.5e-4 -1.12872,-0.063 -1.809301,-0.70325 -0.207414,-0.19512 -0.404128,-0.36474 -0.437142,-0.37694 -0.03301,-0.0122 -0.382514,0.18149 -0.776668,0.43041 -1.208429,0.76315 -1.701728,1.00218 -2.167425,1.05024 -0.380031,0.0392 -0.474752,0.0161 -0.728674,-0.17753 -0.160524,-0.12243 -0.35134,-0.22261 -0.424035,-0.22261 -0.115517,0 -0.462674,0.14978 -1.035266,0.44668 -0.308956,0.16019 -1.014128,0.3133 -1.447652,0.31432 -0.346803,8.1e-4 -0.467175,-0.0388 -0.623703,-0.20546 -0.218302,-0.23238 -0.210009,-0.47722 0.03962,-1.16966 0.109064,-0.30253 0.121416,-0.43097 0.04975,-0.51732 -0.169731,-0.20451 -0.48259,-0.0509 -1.248331,0.6131 -1.417624,1.22919 -2.111428,1.59171 -2.369868,1.23828 -0.146174,-0.19991 -0.0059,-0.4658 0.246085,-0.46634 0.196479,-4.2e-4 1.229088,-0.74129 1.980949,-1.42127 0.727166,-0.65764 1.115506,-0.88126 1.530417,-0.88126 0.161612,0 0.349358,0.0555 0.417212,0.12337 0.166317,0.16631 0.160684,0.96608 -0.0097,1.37381 -0.164144,0.39285 -0.170916,0.71187 -0.01637,0.77117 0.330685,0.1269 2.200348,-0.76545 2.915912,-1.3917 0.798385,-0.69873 0.918309,-0.78065 1.062049,-0.72549 0.27313,0.10481 0.122452,0.42561 -0.541157,1.15217 -0.268776,0.29428 -0.279924,0.38617 -0.06342,0.52274 0.254387,0.16046 0.949869,-0.13401 2.024632,-0.85723 0.94989,-0.6392 1.034451,-0.68519 1.271919,-0.69172 0.170351,-0.005 0.311883,0.0976 0.58312,0.42161 1.015289,1.21264 2.206379,1.12966 4.970604,-0.34632 1.3233,-0.70659 1.72474,-1.03822 1.90697,-1.57536 0.0767,-0.22612 0.2499,-0.71592 0.38485,-1.08845 0.42636,-1.17697 0.90913,-2.59793 1.15062,-3.38667 0.12831,-0.4191 0.2696,-0.85253 0.31398,-0.96317 0.0444,-0.11065 0.17804,-0.56785 0.29704,-1.016 0.119,-0.44816 0.30988,-1.15773 0.42419,-1.57683 0.11431,-0.4191 0.24258,-0.89692 0.28504,-1.06183 0.11392,-0.44233 0.67577,-1.87116 0.81262,-2.06654 0.0651,-0.0929 0.11835,-0.23084 0.11835,-0.30646 0,-0.0756 0.0533,-0.24812 0.11854,-0.38333 0.0652,-0.13521 0.2217,-0.46776 0.3478,-0.73899 0.27851,-0.59906 0.73943,-1.09064 0.95195,-1.01528 0.23881,0.0847 0.42539,0.27823 0.48071,0.49866 0.0628,0.25024 0.30878,0.27026 0.62793,0.0511 0.84393,-0.57946 2.70923,-1.34841 4.33107,-1.78545 0.20955,-0.0565 0.4953,-0.14071 0.635,-0.18719 0.38614,-0.12849 1.39741,-0.41113 1.778,-0.49694 0.90705,-0.20449 2.51357,-0.51438 3.09034,-0.59612 0.25611,-0.0363 0.86571,-0.12377 1.35466,-0.19438 3.68651,-0.53239 7.63076,-0.47042 9.86367,0.15498 0.81768,0.22902 1.66476,0.5391 1.98967,0.72833 0.0699,0.0407 0.2794,0.15516 0.46566,0.2544 1.02216,0.54457 2.4939,2.1378 2.86559,3.10215 0.18493,0.47977 0.25299,1.37639 0.13823,1.82079 -0.14101,0.54602 -0.49336,1.23655 -0.90827,1.78001 -0.4072,0.53338 -1.80646,1.82049 -2.19301,2.01725 -0.33212,0.16905 -1.98185,1.26395 -2.18333,1.44904 -0.0903,0.0829 -0.21593,0.15077 -0.27924,0.15077 -0.0633,0 -0.32356,0.13335 -0.57834,0.29633 -0.25479,0.16299 -0.4852,0.29634 -0.51203,0.29634 -0.0268,0 -0.16535,0.083 -0.30781,0.18443 -0.14246,0.10144 -0.9498,0.53317 -1.7941,0.95939 -0.8443,0.42622 -1.78137,0.91196 -2.08239,1.07942 -0.30101,0.16747 -0.8521,0.42434 -1.22463,0.57082 -0.83882,0.32983 -1.75138,0.73358 -2.96716,1.31279 -0.51433,0.24504 -1.14298,0.53478 -1.397,0.64388 -0.58792,0.25251 -1.67392,0.80872 -2.30875,1.18247 -0.27074,0.15939 -0.56575,0.33128 -0.65559,0.38198 -0.0898,0.0507 -0.23327,0.15548 -0.31875,0.23283 -0.0855,0.0774 -0.19316,0.14065 -0.23929,0.14065 -0.12893,0 -0.93908,0.62647 -1.13927,0.88097 -0.51806,0.65861 0.3753,1.58909 2.59114,2.6988 0.18627,0.0933 0.41487,0.1986 0.508,0.23402 0.0931,0.0354 0.41699,0.15941 0.71967,0.27552 1.03862,0.39841 2.02242,0.61224 3.38667,0.7361 0.58208,0.0529 1.17263,0.11251 1.31233,0.13257 0.5074,0.0729 2.43766,0.10408 2.86437,0.0463 0.2407,-0.0326 0.52841,-0.0377 0.63937,-0.0115 0.1858,0.044 0.19042,0.0572 0.0585,0.16664 -0.35858,0.2976 -2.73003,0.42002 -4.74758,0.2451 z m -17.67632,-8.51178 c 0.12924,-0.17026 0.40841,-0.50281 0.62037,-0.73901 0.44738,-0.49854 0.5841,-0.69153 1.14246,-1.61272 0.24668,-0.40697 0.30937,-0.53176 0.7029,-1.39907 0.24284,-0.53521 0.98789,-2.82763 1.10068,-3.38666 0.39207,-1.94322 0.53469,-3.32892 0.43133,-4.191 -0.0391,-0.32597 -0.073,-0.6403 -0.0754,-0.6985 -0.002,-0.0582 -0.0607,-0.10584 -0.12962,-0.10584 -0.1271,0 -0.47671,0.58469 -0.52761,0.88236 -0.0153,0.0895 -0.0649,0.20733 -0.11015,0.26189 -0.17903,0.21571 -1.6923,4.45233 -1.6923,4.73783 0,0.0931 -0.13891,0.56511 -0.64846,2.20359 -0.0724,0.23283 -0.14807,0.51858 -0.16814,0.635 -0.0201,0.11642 -0.12684,0.45932 -0.23726,0.762 -0.11043,0.30268 -0.32287,0.91228 -0.4721,1.35467 -0.14924,0.44238 -0.36011,0.97982 -0.46861,1.19431 -0.14315,0.283 -0.17178,0.41546 -0.10438,0.48287 0.12903,0.12903 0.34855,-0.003 0.6363,-0.38172 z m 24.6307,8.39887 c -0.0723,-0.18845 0.0281,-0.381 0.19864,-0.381 0.23345,0 0.34557,0.18536 0.21781,0.36007 -0.13267,0.18144 -0.35065,0.1924 -0.41645,0.0209 z m -34.415741,-9.12256 c -0.167711,-0.16771 -0.107727,-0.38373 0.159368,-0.57391 0.414481,-0.29514 0.658472,-0.0853 0.373652,0.32131 -0.186116,0.26572 -0.412579,0.37304 -0.53302,0.2526 z"
            id="path1"
          />
        </g>
      </svg>
    </div>
  );
};

export default ArielSig;
