#!/bin/bash
# Shell script for production deployment (Terraform, Lambda, Frontend)

# Configuration
REGION="us-west-2"
LAMBDA_S3_BUCKET="prod-thealpinestudio-lambda-functions-v1"
TERRAFORM_STATE_S3_BUCKET="prod-thealpinestudio-backend-tf"
FRONTEND_S3_BUCKET="thealpinestudio.com"
CLOUDFRONT_DISTRIBUTION_ID="ETN10ORXOMMDL"  # Production environment CloudFront ID

# Function to check if command succeeded
check_success() {
  if [ $? -ne 0 ]; then
    echo "❌ Error: $1 failed"
    exit 1
  else
    echo "✅ $1 succeeded"
  fi
}

# Confirm production deployment
echo "⚠️ WARNING: You are about to deploy to PRODUCTION environment!"
echo "This will affect the live website at https://thealpinestudio.com"
read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Deployment cancelled."
    exit 1
fi

# Step 1: Create necessary S3 buckets if they don't exist
echo "🔍 Checking if S3 buckets exist..."

# Check Terraform state bucket
aws s3api head-bucket --bucket $TERRAFORM_STATE_S3_BUCKET 2>/dev/null
if [ $? -ne 0 ]; then
    echo "🪣 Creating Terraform state bucket: $TERRAFORM_STATE_S3_BUCKET"
    aws s3 mb s3://$TERRAFORM_STATE_S3_BUCKET --region $REGION
    check_success "Creating Terraform state bucket"
else
    echo "✅ Terraform state bucket already exists: $TERRAFORM_STATE_S3_BUCKET"
fi

# Check Lambda functions bucket
aws s3api head-bucket --bucket $LAMBDA_S3_BUCKET 2>/dev/null
if [ $? -ne 0 ]; then
    echo "🪣 Creating Lambda functions bucket: $LAMBDA_S3_BUCKET"
    aws s3 mb s3://$LAMBDA_S3_BUCKET --region $REGION
    check_success "Creating Lambda functions bucket"
else
    echo "✅ Lambda functions bucket already exists: $LAMBDA_S3_BUCKET"
fi

# Step 2: Deploy Terraform infrastructure
echo "🏗️ Deploying Terraform infrastructure..."

# Navigate to Terraform directory
cd terraform/production/prod-env || exit 1

# Initialize Terraform
echo "🔄 Initializing Terraform..."
terraform init
check_success "Terraform init"

# Plan the deployment
echo "📝 Planning Terraform deployment..."
terraform plan -out=tfplan
check_success "Terraform plan"

# Confirm before applying
read -p "Review the plan above. Continue with apply? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Terraform apply cancelled."
    exit 1
fi

# Apply the deployment
echo "🚀 Applying Terraform deployment..."
terraform apply tfplan
check_success "Terraform apply"

# Return to root directory
cd ../../..

# Step 3: Build and deploy Lambda functions
echo "⚙️ Building and deploying Lambda functions..."

# Array of Lambda functions
LAMBDA_FUNCTIONS=(
    "lambda/prod/ListProducts:listProducts.go:bootstrap:prod-list-products.zip:list-products.zip:prod-list-products"
    "lambda/prod/CheckoutProducts:checkoutProducts.go:bootstrap:prod-checkout-products.zip:checkout-products.zip:prod-checkout-products"
    "lambda/prod/ContactEmail:contactEmail.go:bootstrap:prod-contact-email.zip:contact-email.zip:prod-contact-email"
    "lambda/prod/Webhooks:emailWebhook.go:bootstrap:prod-webhooks.zip:webhooks.zip:prod-webhooks"
)

# Loop through Lambda functions
for FUNCTION in "${LAMBDA_FUNCTIONS[@]}"; do
    # Split the function info
    IFS=':' read -r DIRECTORY SOURCE_FILE OUTPUT_FILE ZIP_FILE S3_KEY FUNCTION_NAME <<< "$FUNCTION"
    
    echo "🔨 Building and deploying: $FUNCTION_NAME"
    
    # Navigate to the function directory
    cd "$DIRECTORY" || exit 1
    
    # Build the Go binary
    echo "🔧 Building Go binary: $SOURCE_FILE -> $OUTPUT_FILE"
    GOOS=linux GOARCH=amd64 go build -o "$OUTPUT_FILE" "$SOURCE_FILE"
    check_success "Building Go binary for $FUNCTION_NAME"
    
    # Create zip file
    echo "📦 Creating zip file: $ZIP_FILE"
    zip "$ZIP_FILE" "$OUTPUT_FILE"
    check_success "Creating zip file for $FUNCTION_NAME"
    
    # Upload to S3
    echo "📤 Uploading to S3: $ZIP_FILE -> s3://$LAMBDA_S3_BUCKET/$S3_KEY"
    aws s3 cp "$ZIP_FILE" "s3://$LAMBDA_S3_BUCKET/$S3_KEY" --region $REGION
    check_success "Uploading to S3 for $FUNCTION_NAME"
    
    # Update Lambda function
    echo "🔄 Updating Lambda function: $FUNCTION_NAME"
    aws lambda update-function-code --function-name "$FUNCTION_NAME" --s3-bucket "$LAMBDA_S3_BUCKET" --s3-key "$S3_KEY" --region $REGION
    check_success "Updating Lambda function $FUNCTION_NAME"
    
    # Return to root directory
    cd - > /dev/null
    
    echo "✅ Completed deployment for: $FUNCTION_NAME"
    echo "-------------------------------------------"
done

# Step 4: Build and deploy frontend
echo "🖥️ Building and deploying frontend..."

# Navigate to the client directory
cd client || exit 1

# Install dependencies
echo "📦 Installing dependencies..."
npm ci
check_success "Installing dependencies"

# Build the frontend for production environment
echo "🔨 Building frontend for production environment..."
npm run build:prod
check_success "Building frontend for production environment"

# Confirm before deploying to production
read -p "About to deploy to PRODUCTION S3 bucket. Continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Frontend deployment cancelled."
    exit 1
fi

# Deploy to S3
echo "📤 Deploying to S3: s3://$FRONTEND_S3_BUCKET/"
aws s3 cp build/ "s3://$FRONTEND_S3_BUCKET/" --recursive --region $REGION
check_success "Deploying to S3"

# Return to root directory
cd ..

# Step 5: Invalidate CloudFront cache
echo "🔄 Invalidating CloudFront cache..."
aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --paths "/*"
check_success "Invalidating CloudFront cache"

echo "🎉 Production deployment completed successfully!"
