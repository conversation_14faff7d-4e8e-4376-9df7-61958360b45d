// src/pages/Shop.tsx

import React, { useState, useEffect, useCallback, useRef } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  useMediaQuery,
  TextField,
  Chip,
  OutlinedInput,
  SelectChangeEvent,
  Checkbox,
  ListItemText,
} from "@mui/material";
import { QUERY_PARAMS } from "../contants/queryParams";
import {
  categories,
  subcategoriesMap,
  Product,
  Subcategory,
  Category,
} from "../admin/auth/types";
import ProductGallery from "../Components/Products/ProductGallery";
import theme from "../Context/theme";
import { useProducts } from "../Context/ProductContext";
import debounce from "lodash/debounce";
import { isValidCategory } from "../utils/typeGaurds";

const Shop: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();

  // Extract initial query parameters or set defaults
  const initialCategoryParam = searchParams.get(
    QUERY_PARAMS.CATEGORY
  ) as Category;
  const initialCategory = isValidCategory(initialCategoryParam || "")
    ? (initialCategoryParam as Category)
    : "All";

  const initialSortOrder = searchParams.get(QUERY_PARAMS.SORT) || "asc";
  const initialPage = parseInt(searchParams.get(QUERY_PARAMS.PAGE) || "1", 10);
  const initialLimit = parseInt(
    searchParams.get(QUERY_PARAMS.LIMIT) || "12",
    10
  );
  const initialSearch = searchParams.get(QUERY_PARAMS.SEARCH) || "";


  const [selectedCategory, setSelectedCategory] = useState<Category | "All">(
    initialCategory
  );
  const [sortOrder, setSortOrder] = useState<string>(initialSortOrder);
  const [currentPage, setCurrentPage] = useState<number>(initialPage);
  const productsPerPage = initialLimit;
  const [searchKeyword, setSearchKeyword] = useState<string>(initialSearch);
  const [selectedSubcategories, setSelectedSubcategories] = useState<
    Subcategory[]
  >([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [errorMessage, _setErrorMessage] = useState<string | null>(null);
  const [products, setProducts] = useState<Product[]>([]);

  const isSmallScreen = useMediaQuery(theme.breakpoints.down("sm"));

  const { fetchProducts } = useProducts();

  const getSubcategories = (): Subcategory[] => {
    return subcategoriesMap[selectedCategory] || [];
  };

  // Flag to detect initial mount
  const isInitialMount = useRef(true);

  // Update URL when filters or pagination change, except on initial mount
  useEffect(() => {
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    const params: Record<string, string> = {};

    if (selectedCategory !== "All") {
      params[QUERY_PARAMS.CATEGORY] = selectedCategory;
    }

    if (selectedSubcategories.length > 0) {
      params[QUERY_PARAMS.SUBCATEGORIES] = selectedSubcategories.join(",");
    }

    if (sortOrder) {
      params[QUERY_PARAMS.SORT] = sortOrder;
    }

    if (searchKeyword) {
      params[QUERY_PARAMS.SEARCH] = searchKeyword;
    }

    params[QUERY_PARAMS.PAGE] = currentPage.toString();
    params[QUERY_PARAMS.LIMIT] = productsPerPage.toString();

    // Only update searchParams if params have actually changed
    const currentParams: Record<string, string> = {};
    searchParams.forEach((value, key) => {
      currentParams[key] = value;
    });
    const paramsChanged =
      JSON.stringify(currentParams) !== JSON.stringify(params);

    if (paramsChanged) {
      setSearchParams(params);
    }
  }, [
    selectedCategory,
    sortOrder,
    searchKeyword,
    currentPage,
    productsPerPage,
    selectedSubcategories,
    setSearchParams,
    searchParams,
  ]);

  // Debounced function to fetch products
  const debouncedFetch = useCallback(
    debounce(async () => {
      setIsLoading(true); // ✅ Ensure loading state is set before fetching
      try {
        const productsData = await fetchProducts({
          category: selectedCategory !== "All" ? selectedCategory : undefined,
          subcategories:
            selectedSubcategories.length > 0
              ? selectedSubcategories
              : undefined,
          sortOrder,
          searchKeyword,
          page: currentPage,
          limit: 12,
        });
        setProducts(productsData);
      } catch (error) {
      } finally {
        setIsLoading(false);
      }
    }, 300),
    [
      fetchProducts,
      selectedCategory,
      selectedSubcategories,
      sortOrder,
      searchKeyword,
      currentPage,
    ]
  );

  // ✅ Prevents `useEffect` from running multiple times unnecessarily
  useEffect(() => {
    debouncedFetch();
    return () => debouncedFetch.cancel();
  }, [debouncedFetch]);

  const handleSelectProduct = (product: Product) => {
    navigate(`/product/${product.id}`);
  };

  // Handle multiple subcategory selection
  const handleSubcategoryChange = (event: SelectChangeEvent<string[]>) => {
    const {
      target: { value },
    } = event;
    setSelectedSubcategories(
      typeof value === "string"
        ? (value.split(",") as Subcategory[])
        : (value as Subcategory[])
    );
    setCurrentPage(1); // Reset to first page on filter change
  };

  //Form control styles
  const formControlStyles = {
    bgcolor: "white",
    minWidth: 200,
    maxWidth: 250,
    borderRadius: 1,
    "& .MuiOutlinedInput-root": {
      "& fieldset": { borderColor: theme.palette.rose.main },
      "&:hover fieldset": { borderColor: theme.palette.rose.main },
      "&.Mui-focused fieldset": { borderColor: theme.palette.lightBlue.dark },
    },
    "& .MuiInputLabel-root": {
      color: theme.palette.lightBlue.main,
      "&.Mui-focused": { color: theme.palette.lightBlue.dark },
      "&.MuiInputLabel-shrink": {
        transform: "translate(14px, -2px) scale(0.75) !important",
      },
    },
  };

  return (
    <Box
      sx={{
        bgcolor: "lightBlue.main",
        textAlign: "center",
        py: { xs: 6, sm: 8, md: 10 }, // Matches About page padding
      }}
    >
      <Typography
        variant={isSmallScreen ? "h4" : "h3"}
        component="h1"
        gutterBottom
        sx={{
          pt: 8,
          mb: 2,
          fontWeight: 700,
          color: "rose.main",
          letterSpacing: ".3rem",
        }}
      >
        Shop
      </Typography>

      {/* Sorting, Filtering, and Search Controls */}
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          mb: 4,
          flexWrap: "wrap",
          gap: 2,
        }}
      >
        {/* Category Filter */}
        <FormControl variant="outlined" sx={formControlStyles}>
          <InputLabel id="category-filter-label" shrink>
            Filter by Category
          </InputLabel>
          <Select
            labelId="category-filter-label"
            id="category-filter"
            value={selectedCategory}
            onChange={(e) => {
              setSelectedCategory(e.target.value as Category);
              setSelectedSubcategories([]); // Reset subcategories when category changes
              setCurrentPage(1); // Reset to first page on filter change
            }}
            label="Filter by Category"
            aria-label="Filter products by category"
          >
            {categories.map((categoryOption) => (
              <MenuItem key={categoryOption} value={categoryOption}>
                {categoryOption}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Subcategory Filter */}
        {getSubcategories().length > 0 && (
          <FormControl variant="outlined" sx={formControlStyles}>
            <InputLabel id="subcategory-filter-label">
              Filter by Subcategory
            </InputLabel>
            <Select
              labelId="subcategory-filter-label"
              id="subcategory-filter"
              multiple
              value={selectedSubcategories}
              onChange={handleSubcategoryChange}
              input={<OutlinedInput label="Filter by Subcategory" />}
              renderValue={(selected) => (
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 0.5 }}>
                  {(selected as Subcategory[]).map((value) => (
                    <Chip key={value} label={value} />
                  ))}
                </Box>
              )}
              aria-label="Filter products by subcategory"
            >
              {getSubcategories().map((subcategoryOption) => (
                <MenuItem key={subcategoryOption} value={subcategoryOption}>
                  <Checkbox
                    checked={selectedSubcategories.includes(subcategoryOption)}
                  />
                  <ListItemText primary={subcategoryOption} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        )}

        {/* Sort Order */}
        <FormControl variant="outlined" sx={formControlStyles}>
          <InputLabel id="sort-order-label">Sort Order</InputLabel>
          <Select
            labelId="sort-order-label"
            id="sort-order"
            value={sortOrder}
            onChange={(e) => {
              setSortOrder(e.target.value);
              setCurrentPage(1); // Reset to first page on sort change
            }}
            label="Sort Order"
            aria-label="Sort products by order"
          >
            <MenuItem value="asc">Ascending</MenuItem>
            <MenuItem value="desc">Descending</MenuItem>
          </Select>
        </FormControl>

        {/* Search Input */}
        <FormControl variant="outlined" sx={formControlStyles}>
          <TextField
            id="search"
            label="Search"
            variant="outlined"
            value={searchKeyword}
            onChange={(e) => {
              setSearchKeyword(e.target.value);
              setCurrentPage(1); // Reset to first page on search
            }}
            sx={formControlStyles}
          />
        </FormControl>
      </Box>

      {/* Loading Indicator */}
      {isLoading && (
        <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {!isLoading && !errorMessage && products.length === 0 && (
        <Typography variant="h6" align="center" sx={{ mt: 4 }}>
          No products found.
        </Typography>
      )}

      {!isLoading && !errorMessage && products.length > 0 && (
        <ProductGallery
          onSelectProduct={handleSelectProduct}
          products={products}
        />
      )}

      {/* Pagination Controls */}
      {/* Uncomment and implement pagination if needed
      {!isLoading && !errorMessage && totalPages > 1 && (
        <Box sx={{ display: "flex", justifyContent: "center", mt: 4 }}>
          <Pagination
            page={currentPage}
            onChange={handlePageChange}
            color="primary"
          />
        </Box>
      )}
      */}
    </Box>
  );
};

export default Shop;
