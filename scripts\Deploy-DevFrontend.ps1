# PowerShell script to build and deploy the frontend for the development environment

# Configuration
$REGION = "us-west-2"
$PROFILE = "thealpinestudio"
$S3_BUCKET = "test.thealpinestudio.com-v1"
$CLOUDFRONT_DISTRIBUTION_ID = "E333R3CHKXLYGZ"
$API_BASE_URL = "https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test"

Write-Host "=== Starting Development Frontend Deployment ==="

# Navigate to client directory
Push-Location client

try {
    # Install dependencies if needed
    if (-not (Test-Path -Path "node_modules")) {
        Write-Host "Installing dependencies..."
        npm ci
        if ($LASTEXITCODE -ne 0) {
            throw "Failed to install dependencies."
        }
    }

    # Set environment variables for development
    Write-Host "Setting environment variables for development..."
    $env:NODE_ENV = "development"
    $env:REACT_APP_ENVIRONMENT = "development"
    $env:REACT_APP_CART_API_ROUTE = "$API_BASE_URL/dev-checkout-products"
    $env:REACT_APP_PRODUCT_API_ROUTE = "$API_BASE_URL/dev-list-products"
    $env:REACT_APP_CONTACT_API_ROUTE = "$API_BASE_URL/dev-contact-email"
    $env:REACT_APP_WEBHOOKS_API_ROUTE = "$API_BASE_URL/dev-webhooks"
    
    # Get Stripe public key from AWS Parameter Store
    Write-Host "Getting Stripe public key from AWS Parameter Store..."
    $STRIPE_PUBLIC_KEY = aws ssm get-parameter --name "/dev/stripe/public-key" --with-decryption --query "Parameter.Value" --output text --region $REGION --profile $PROFILE
    if ($LASTEXITCODE -ne 0) {
        Write-Host "Warning: Could not retrieve Stripe public key from Parameter Store. Using default test key."
        $STRIPE_PUBLIC_KEY = "pk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL"
    }
    $env:REACT_APP_STRIPE_PUBLIC_KEY = $STRIPE_PUBLIC_KEY
    $env:REACT_APP_S3_BUCKET = "dev-thealpinestudio-hosting"

    # Build the frontend
    Write-Host "Building frontend for development environment..."
    npm run build:dev
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to build frontend."
    }

    # Deploy to S3
    Write-Host "Deploying to S3 bucket: $S3_BUCKET..."
    aws s3 cp build/ "s3://$S3_BUCKET/" --recursive --region $REGION --profile $PROFILE
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to deploy to S3."
    }

    # Invalidate CloudFront cache
    Write-Host "Invalidating CloudFront cache for distribution: $CLOUDFRONT_DISTRIBUTION_ID..."
    aws cloudfront create-invalidation --distribution-id $CLOUDFRONT_DISTRIBUTION_ID --paths "/*" --region $REGION --profile $PROFILE
    if ($LASTEXITCODE -ne 0) {
        throw "Failed to invalidate CloudFront cache."
    }

    Write-Host "=== Development Frontend Deployment Completed Successfully ==="
}
catch {
    Write-Host "Error: $_" -ForegroundColor Red
    Write-Host "=== Development Frontend Deployment Failed ==="
    exit 1
}
finally {
    # Return to original directory
    Pop-Location
}
