variable "rest_api_name" {
  type        = string
  description = "Name of the API Gateway for Lambda"
  default     = "test-lambda-api-gateway"
}

variable "api_gateway_region" {
  type        = string
  description = "The region in which to create/manage resources"
}

variable "api_gateway_account_id" {
  type        = string
  description = "The account ID in which to create/manage resources"
}

variable "api_stage_name" {
  type        = string
  description = "The name of the API Gateway stage"
  default     = "dev"
}

variable "root_domain" {
  type        = string
  description = "The domain name to associate with the API"
  default     = "test.thealpinestudio.com" //replace your root domain name here
}
variable "sub_domain" {
  type        = string
  description = "The subdomain for the API"
  default     = "api.test.thealpinestudio.com" //replace your subdomain name here
}

variable "list_lambda_function_arn" {
  description = "The ARN of the Lambda function that handles CORS preflight requests"
  type        = string
}
variable "lambda_function_checkout_arn" {
  description = "The ARN of the Lambda function that handles CORS preflight requests"
  type        = string
}
variable "lambda_function_webhooks_arn" {
  description = "The ARN of the Lambda function that handles CORS preflight requests"
  type        = string
}
variable "lambda_function_contact_arn" {
  description = "The ARN of the Lambda function that handles CORS preflight requests"
  type        = string
}

variable "list_products_function" {
  type = string
  description = "the name of the lambda list function"
  default = "test-list-products"
}

variable "checkout_products_function" {
  type = string
  description = "Lmabda Checkout Function"
  default = "test-checkout-products"
}

variable "webhooks_function" {
  type = string
  description = "Lmabda Checkout Function"
  default = "test-webhooks"
}

variable "contact_email_function" {
  type = string
  description = "Lmabda Checkout Function"
  default = "test-contact-email"
}



variable "cognito_provider_name" {
  type        = string
  description = "Test Issuer for Authentication coming from Cognito"
}
variable "cognito_user_pool_id" {
  type = string
}
variable "region" {
  type    = string
  default = "us-west-2"
}
variable "cognito_app_client_id" {
  type        = string
  description = "This is the Cognito App/Client Id"
}
variable "cognito_user_pool_arn" {
  type        = string
  description = "Test Cognito user pool ARN"
}
