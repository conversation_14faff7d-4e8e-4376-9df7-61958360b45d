# resource "aws_api_gateway_resource" "cors_resource" {
#   rest_api_id = aws_api_gateway_rest_api.rest_api.id
#   parent_id   = aws_api_gateway_rest_api.rest_api.root_resource_id
#   path_part   = "{cors+}"
# }
resource "aws_api_gateway_method" "cors_method" {
  rest_api_id   = aws_api_gateway_rest_api.rest_api.id
  resource_id   = aws_api_gateway_resource.rest_api_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}
resource "aws_api_gateway_integration" "cors_integration" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  resource_id = aws_api_gateway_resource.rest_api_resource.id
  http_method = aws_api_gateway_method.cors_method.http_method
  type        = "MOCK"
  passthrough_behavior = "NEVER"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
  timeout_milliseconds    = 29000
  depends_on = [aws_api_gateway_method.cors_method]
}
resource "aws_api_gateway_method_response" "cors_response" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  resource_id = aws_api_gateway_resource.rest_api_resource.id
  http_method = aws_api_gateway_method.cors_method.http_method
  status_code = 200
  depends_on = [aws_api_gateway_integration.cors_integration, aws_api_gateway_method_response.cors_response]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Origin"  = true,
    "method.response.header.Access-Control-Allow-Methods" = true,
    "method.response.header.Access-Control-Allow-Headers" = true,
    "method.response.header.Access-Control-Allow-Credentials" = true,
    "method.response.header.Access-Control-Expose-Headers" = true
  }
  response_models = {
    "application/json" = "Empty"
  }
  
}
resource "aws_api_gateway_integration_response" "cors_integration_response" {
  depends_on  = [aws_api_gateway_integration.cors_integration, aws_api_gateway_method_response.cors_response]
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  resource_id = aws_api_gateway_resource.rest_api_resource.id
  http_method = aws_api_gateway_method.cors_method.http_method
  status_code = 200
   response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Methods" = "'OPTIONS,POST,GET'",
    "method.response.header.Access-Control-Allow-Origin"  = "'https://thealpinestudio.com'",
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}
