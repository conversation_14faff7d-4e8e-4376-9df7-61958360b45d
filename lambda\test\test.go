package main

import (
    "log"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/ses"
)

var (
    senderEmail    = "<EMAIL>" // Replace with your verified sender email
    recipientEmail = "<EMAIL>"     // Replace with your recipient email
)

func sendEmail(to string, challengeCode string) {
    log.Printf("Sending email to: %s with code: %s", to, challengeCode)

    sess := session.Must(session.NewSession(&aws.Config{
        Region: aws.String("us-west-2"),
    }))
    svc := ses.New(sess)
    input := &ses.SendEmailInput{
        Destination: &ses.Destination{
            ToAddresses: []*string{aws.String(to)},
        },
        Message: &ses.Message{
            Body: &ses.Body{
                Text: &ses.Content{
                    Charset: aws.String("UTF-8"),
                    Data:    aws.String("Your verification code is " + challengeCode),
                },
            },
            Subject: &ses.Content{
                Charset: aws.String("UTF-8"),
                Data:    aws.String("Your verification code"),
            },
        },
        Source: aws.String(senderEmail),
    }

    result, err := svc.SendEmail(input)
    if err != nil {
        log.Printf("Failed to send email: %v", err)
    } else {
        log.Printf("Email sent successfully: %v", result)
    }
}

func main() {
    // Call the sendEmail function with a test challenge code
    sendEmail(recipientEmail, "123456")
}
