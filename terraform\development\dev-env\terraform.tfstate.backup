{"version": 4, "terraform_version": "1.10.5", "serial": 447, "lineage": "53414a2b-5148-fd68-3224-a4ef435ede6b", "outputs": {}, "resources": [{"module": "module.lambda_function", "mode": "managed", "type": "aws_s3_bucket", "name": "s3_bucket", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"acceleration_status": "", "acl": null, "arn": "arn:aws:s3:::dev-thealpinestudio-lambda-functions-v1", "bucket": "dev-thealpinestudio-lambda-functions-v1", "bucket_domain_name": "dev-thealpinestudio-lambda-functions-v1.s3.amazonaws.com", "bucket_prefix": "", "bucket_regional_domain_name": "dev-thealpinestudio-lambda-functions-v1.s3.us-west-2.amazonaws.com", "cors_rule": [{"allowed_headers": ["*"], "allowed_methods": ["POST", "GET", "PUT"], "allowed_origins": ["https://test.thealpinestudio.com", "https://thealpinestudio.com"], "expose_headers": [], "max_age_seconds": 3000}], "force_destroy": false, "grant": [{"id": "49f744bf851c1df61a3ea6002103b48141213c5416dac9ce3b0d5bd8a58045e4", "permissions": ["FULL_CONTROL"], "type": "CanonicalUser", "uri": ""}], "hosted_zone_id": "Z3BJ6K6RIION7M", "id": "dev-thealpinestudio-lambda-functions-v1", "lifecycle_rule": [], "logging": [], "object_lock_configuration": [], "object_lock_enabled": false, "policy": "", "region": "us-west-2", "replication_configuration": [], "request_payer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "server_side_encryption_configuration": [{"rule": [{"apply_server_side_encryption_by_default": [{"kms_master_key_id": "", "sse_algorithm": "AES256"}], "bucket_key_enabled": false}]}], "tags": {}, "tags_all": {}, "timeouts": null, "versioning": [{"enabled": false, "mfa_delete": false}], "website": [], "website_domain": null, "website_endpoint": null}, "sensitive_attributes": [], "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInJlYWQiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19"}]}], "check_results": null}