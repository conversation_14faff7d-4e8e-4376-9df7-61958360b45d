#DEVELOPMENT ENVIRONMENT
provider "aws" {
  region  = "us-west-2"
  profile = "thealpinestudio"
  # Using profile instead of access_key/secret_key
  # access_key = var.access_key
  # secret_key = var.secret_key
}

module "website_s3_bucket" {
  source              = "../dev-modules/dev-s3_hosting"
  hosting_bucket_name = var.hosting_bucket_name
  zone_id             = var.zone_id
  domain_name         = var.domain_name
}

module "cognito" {
  source                 = "../dev-modules/dev-cognito"
  cognito_user_pool_arn  = ""
  user_pool_name         = "dev-alpinestudio-user-pool"
  api_gateway_id         = var.api_gateway_id
  api_gateway_account_id = var.api_gateway_account_id
  api_gateway_region     = var.api_gateway_region
}


module "lambda_function" {
  source                      = "../dev-modules/dev-lambda"
  account_id                  = var.account_id
  list_products_function_name = var.list_products_function_name
  lambda_function_arn         = module.lambda_function.lambda_function_arn
  api_gateway_arn             = module.api_gateway.api_gateway_arn
  api_gateway_id              = module.api_gateway.api_gateway_id
  # cognito_user_pool_arn           = module.cognito.cognito_user_pool_arn
  api_gateway_account_id = var.api_gateway_account_id
  api_gateway_region     = var.api_gateway_region
  stripe_secret_key      = var.stripe_secret_key
  stripe_endpoint_secret = var.stripe_endpoint_secret
  resend_api_key         = var.resend_api_key
  frontend_url           = var.frontend_url
  dynamodb_orders_table  = var.dynamodb_orders_table
}


module "api_gateway" {
  source                       = "../dev-modules/dev-api-gateway"
  api_gateway_region           = var.region
  api_gateway_account_id       = var.account_id
  list_lambda_function_arn     = module.lambda_function.list_lambda_function_arn
  lambda_function_checkout_arn = module.lambda_function.lambda_function_checkout_arn
  lambda_function_webhooks_arn = module.lambda_function.lambda_function_webhooks_arn
  lambda_function_contact_arn  = module.lambda_function.lambda_function_contact_arn
  cognito_provider_name        = module.cognito.cognito_provider_name
  cognito_user_pool_id         = module.cognito.cognito_user_pool_id
  cognito_app_client_id        = module.cognito.cognito_app_client_id
  cognito_user_pool_arn        = module.cognito.cognito_user_pool_arn
}
