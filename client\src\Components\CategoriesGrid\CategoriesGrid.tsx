// src/components/CategoriesGrid/CategoriesGrid.tsx
import React from "react";
import {
  Grid,
  Card,
  CardMedia,
  CardContent,
  Typography,
  CardActionArea,
} from "@mui/material";
import { Category } from "../../admin/auth/types";

import limitedEditionImage from "../../../assets/Limited-Edition-Prints.webp";
import openEditionStickersImage from "../../../assets/Open-Edition-Prints.webp";
import originalsImage from "../../../assets/Original-Works.webp";
import apparelImage from "../../../assets/Stickers-Product.webp";
import allCategoryImage from "../../../assets/GreatHornedOwl.webp";

const categoryImagesMap: Record<Category, string> = {
  All: allCategoryImage,
  Originals: originalsImage,
  "Limited Edition Prints": limitedEditionImage,
  "Open Edition Prints": openEditionStickersImage,
  "Apparel & Stickers": apparelImage,
};

interface CategoriesGridProps {
  categories: Category[];
  onCategoryClick: (category: Category) => void;
}

const CategoriesGrid: React.FC<CategoriesGridProps> = ({
  categories,
  onCategoryClick,
}) => {
  return (
    <Grid container spacing={2} justifyContent="center" sx={{ padding: 4 }}>
      {categories.map((category) => (
        <Grid item key={category} xs={12} sm={6} md={4}>
          <Card
            sx={{
              height: 450, // Fixed overall card height
              borderRadius: 2,
              boxShadow: 3,
              transition: "transform 0.3s ease, box-shadow 0.3s ease",
              display: "flex",
              flexDirection: "column",
              "&:hover": {
                transform: "scale(1.03)",
                boxShadow: 6,
              },
            }}
          >
            <CardActionArea
              sx={{ height: "100%", display: "flex", flexDirection: "column" }}
              onClick={() => onCategoryClick(category)}
            >
              <CardMedia
                component="img"
                image={categoryImagesMap[category]}
                alt={category}
                sx={{
                  height: 350, // Fixed image height
                  width: "100%",
                  objectFit: "cover",
                }}
              />
              <CardContent
                sx={{
                  flexGrow: 1,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  paddingX: 2, // Horizontal padding for extra breathing room
                  textAlign: "center",
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    // Remove truncation properties to allow wrapping
                    whiteSpace: "normal",
                    overflow: "visible",
                    textOverflow: "unset",
                  }}
                >
                  {category}
                </Typography>
              </CardContent>
            </CardActionArea>
          </Card>
        </Grid>
      ))}
    </Grid>
  );
};

export default CategoriesGrid;
