# Alpine Studio - Deployment Process Guide

## Current Deployment Flow

### Test Environment (test.thealpinestudio.com)

#### Automatic Deployment (Recommended)
```bash
# 1. Push changes to dev branch
git checkout dev
git add .
git commit -m "Your changes"
git push origin dev

# 2. GitHub Actions automatically:
#    - Builds Lambda functions
#    - Applies Terraform changes
#    - Builds and deploys frontend
#    - Tests endpoints
```

#### Manual Deployment (Current Method)
```powershell
# Frontend only
.\scripts\Deploy-DevFrontend.ps1

# Full infrastructure
.\scripts\Deploy-DevEnvironment.ps1
```

### Production Deployment Process

#### Step 1: Verify Test Environment
```bash
# 1. Ensure test environment is working
curl https://test.thealpinestudio.com
curl https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test/dev-list-products

# 2. Test all functionality:
#    - Product listing ✅
#    - Product details ✅
#    - Contact form ✅
#    - Checkout process ✅
```

#### Step 2: Prepare Production Release
```bash
# 1. Create release branch from dev
git checkout dev
git pull origin dev
git checkout -b release/v1.x.x

# 2. Update version numbers if needed
# 3. Final testing on release branch
```

#### Step 3: Deploy to Production
```bash
# Option A: GitHub Actions (Recommended)
# 1. Go to GitHub Actions tab
# 2. Select "Production Deployment" workflow
# 3. Click "Run workflow"
# 4. Select main/master branch
# 5. Monitor deployment progress

# Option B: Manual Script
.\scripts\production-deployment.sh
```

#### Step 4: Post-Deployment Verification
```bash
# 1. Test production endpoints
curl https://thealpinestudio.com
curl https://api.thealpinestudio.com/prod-list-products

# 2. Verify all functionality
# 3. Monitor CloudWatch logs
# 4. Check error rates
```

## Environment Variables Summary

### Development Environment
```bash
# Frontend URLs
FRONTEND_URL=https://test.thealpinestudio.com
S3_BUCKET=test.thealpinestudio.com-v1
CLOUDFRONT_ID=E333R3CHKXLYGZ

# API Endpoints
CLIENT_API=https://u3vj4dkndh.execute-api.us-west-2.amazonaws.com/test
ADMIN_API=https://f2trvidc5b.execute-api.us-west-2.amazonaws.com/dev

# Stripe (Test)
STRIPE_PUBLIC_KEY=pk_test_...
STRIPE_SECRET_KEY=sk_test_...

# Other Services
RESEND_API_KEY=re_...
DYNAMODB_TABLE=dev-orders
```

### Production Environment
```bash
# Frontend URLs
FRONTEND_URL=https://thealpinestudio.com
S3_BUCKET=thealpinestudio.com
CLOUDFRONT_ID=ETN10ORXOMMDL

# API Endpoints
CLIENT_API=https://api.thealpinestudio.com
ADMIN_API=https://admin-api.thealpinestudio.com

# Stripe (Live)
STRIPE_PUBLIC_KEY=pk_live_...
STRIPE_SECRET_KEY=sk_live_...

# Other Services
RESEND_API_KEY=re_...
DYNAMODB_TABLE=prod-orders
```

## Deployment Checklist

### Pre-Deployment
- [ ] All tests passing in development
- [ ] Code reviewed and approved
- [ ] Database migrations tested (if any)
- [ ] Environment variables updated
- [ ] Secrets rotated if needed

### During Deployment
- [ ] Monitor deployment logs
- [ ] Watch for errors in CloudWatch
- [ ] Verify API Gateway deployments
- [ ] Check Lambda function updates

### Post-Deployment
- [ ] Smoke test all major features
- [ ] Verify SSL certificates
- [ ] Check CloudFront cache invalidation
- [ ] Monitor error rates for 24 hours
- [ ] Update documentation if needed

## Rollback Procedures

### Frontend Rollback
```bash
# 1. Revert to previous S3 deployment
aws s3 sync s3://backup-bucket/previous-version/ s3://thealpinestudio.com/

# 2. Invalidate CloudFront
aws cloudfront create-invalidation --distribution-id ETN10ORXOMMDL --paths "/*"
```

### Backend Rollback
```bash
# 1. Revert Lambda functions
aws lambda update-function-code --function-name prod-list-products --s3-bucket backup-bucket --s3-key previous-version.zip

# 2. Revert Terraform if needed
cd terraform/production/prod-env
terraform plan -var-file=terraform.tfvars.backup
terraform apply
```

## Monitoring & Alerts

### Key Metrics to Monitor
- API Gateway 4xx/5xx error rates
- Lambda function duration and errors
- CloudFront cache hit ratio
- DynamoDB read/write capacity
- Stripe webhook delivery success

### Recommended Alerts
- API error rate > 5%
- Lambda function errors > 10/hour
- CloudFront origin errors
- DynamoDB throttling events
