region     = "us-west-2"
account_id = "************"
zone_id    = "Z04085477NKG6FGRMKK4"
access_key = "********************"
secret_key = "wrx7xsUyR2l4ZHPTO2WSdT74lWFjFik5lv5Grj5x"
//S3
# Using test.thealpinestudio.com for development (CloudFront ID: E333R3CHKXLYGZ)
domain_name = "test.thealpinestudio.com"

api_gateway_id        = "************"
resend_api_key        = "re_ad1veToT_2yFABNEpT9F3dqUMy73Q6P3t"
frontend_url          = "https://test.thealpinestudio.com"
dynamodb_orders_table = "dev-orders"

hosting_bucket_name = "dev-thealpinestudio-hosting"
api_gateway_region  = "us-west-2"
# Using test keys for development environment
stripe_secret_key      = "sk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL"
stripe_endpoint_secret = "whsec_Kh2KKZZnTceUvS6YCngbv75A2YatEgzy"


pk_test_51NirT5H1KC3YYzQbs2HrEGrXz7TDCVHJBGGPQ9o0TGAraTO2OjZIDEvdA8gpiDwzJU1CtPsAGGvgwI0GPC7MYGsJ00GOiSzTRw

sk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL

# stripe_endpoint_secret = "whsec_3sShnPx9scBOzlPKFhTwX5NnbituJ7kd"
# test_endpoint_seceret = "whsec_Kh2KKZZnTceUvS6YCngbv75A2YatEgzy"
# test_stripe_secret_key = "sk_test_51NirT5H1KC3YYzQbwoT6Tewe6rFzhf3e5xmWrgIw75V01q126czeifShPGVOSf00K8Pblw7FUA5SaBEXfW5VnXb700uS8rSOyL"

api_gateway_account_id = "************"

list_products_function_name = "dev-list-products"
common_tags                 = "The Alpine Studio Test"
