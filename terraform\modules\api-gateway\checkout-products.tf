
resource "aws_api_gateway_resource" "checkout_products_resource" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  parent_id   = aws_api_gateway_rest_api.rest_api.root_resource_id
  path_part   = "checkout-products"
}
# POST method for checkout-products
resource "aws_api_gateway_method" "checkout_products_post_method" {
  rest_api_id   = aws_api_gateway_rest_api.rest_api.id
  resource_id   = aws_api_gateway_resource.checkout_products_resource.id
  http_method   = "POST"
  authorization = "none"
  authorizer_id = aws_api_gateway_authorizer.api_authorizer.id
}

# Integration for checkout-products POST method
resource "aws_api_gateway_integration" "checkout_products_post_integration" {
  rest_api_id             = aws_api_gateway_rest_api.rest_api.id
  resource_id             = aws_api_gateway_resource.checkout_products_resource.id
  http_method             = aws_api_gateway_method.checkout_products_post_method.http_method
  integration_http_method = "POST"
  type                    = "AWS_PROXY"
  uri                     = var.lambda_function_checkout_arn
}
resource "aws_api_gateway_method_response" "checkout_products_post_method_response_200" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  resource_id = aws_api_gateway_resource.checkout_products_resource.id
  http_method = aws_api_gateway_method.checkout_products_post_method.http_method
  status_code = "200"
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = true
    "method.response.header.Access-Control-Allow-Methods"     = true
    "method.response.header.Access-Control-Allow-Origin"      = true
    "method.response.header.Access-Control-Allow-Credentials" = true
  }
}

resource "aws_api_gateway_integration_response" "checkout_products_post_integration_response_200" {
  rest_api_id = aws_api_gateway_rest_api.rest_api.id
  resource_id = aws_api_gateway_resource.checkout_products_resource.id
  http_method = aws_api_gateway_integration.checkout_products_post_integration.http_method
  status_code = aws_api_gateway_method_response.checkout_products_post_method_response_200.status_code
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers"     = "'Content-Type, X-Amz-Date, Authorization, X-Api-Key, X-Amz-Security-Token,X-Requested-With'"
    "method.response.header.Access-Control-Allow-Methods"     = "'OPTIONS,POST'"
    "method.response.header.Access-Control-Allow-Origin"      = "'https://thealpinestudio.com'"
    "method.response.header.Access-Control-Allow-Credentials" = "'true'"
  }
}


resource "aws_lambda_permission" "lambda_api_gw_permissions_checkout" {
  statement_id  = "AllowExecutionFromAPIGateway-${var.checkout_products_function}2"
  action        = "lambda:InvokeFunction"
  function_name = var.checkout_products_function
  principal     = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:${aws_api_gateway_rest_api.rest_api.id}/*/${aws_api_gateway_method.checkout_products_post_method.http_method}${aws_api_gateway_resource.checkout_products_resource.path}"
}