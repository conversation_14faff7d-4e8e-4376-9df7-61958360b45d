// src/data/categoriesData.ts

import LimitedEditionIcon from "@mui/icons-material/Star";
import PrintsStickersIcon from "@mui/icons-material/LocalPrintshop";
import ScratchboardsIcon from "@mui/icons-material/Brush";
import ShirtsHoodiesIcon from "@mui/icons-material/Checkroom";

import { Category } from "../types/Category";

export const categories: Category[] = [
  {
    title: "Limited Edition",
    icon: LimitedEditionIcon,
    path: "/shop/limited-edition",
  },
  {
    title: "Prints and Stickers",
    icon: PrintsStickersIcon,
    path: "/shop/prints-stickers",
  },
  {
    title: "Original Scratchboards",
    icon: ScratchboardsIcon,
    path: "/shop/original-scratchboards",
  },
  {
    title: "Shirts & Hoodies",
    icon: ShirtsHoodiesIcon,
    path: "/shop/shirts-hoodies",
  },
];
