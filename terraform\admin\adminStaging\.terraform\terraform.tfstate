{"version": 3, "terraform_version": "1.9.6", "backend": {"type": "s3", "config": {"access_key": null, "acl": null, "assume_role_duration_seconds": null, "assume_role_policy": null, "assume_role_policy_arns": null, "assume_role_tags": null, "assume_role_transitive_tag_keys": null, "bucket": "admin-thealpinestudio-backend-tf", "dynamodb_endpoint": null, "dynamodb_table": null, "encrypt": null, "endpoint": null, "external_id": null, "force_path_style": null, "iam_endpoint": null, "key": "./.terraform/terraform.tfstate", "kms_key_id": null, "max_retries": null, "profile": null, "region": "us-west-2", "role_arn": null, "secret_key": null, "session_name": null, "shared_credentials_file": null, "skip_credentials_validation": null, "skip_metadata_api_check": null, "skip_region_validation": null, "sse_customer_key": null, "sts_endpoint": null, "token": null, "workspace_key_prefix": null}, "hash": 2385607780}}