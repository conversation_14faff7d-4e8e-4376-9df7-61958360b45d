{"version": "1", "triggerSource": "DefineAuthChallenge_Authentication", "region": "us-west-2", "userPoolId": "us-west-2_GOD2mBQiC", "userName": "testuser", "callerContext": {"awsSdkVersion": "aws-sdk-unknown-unknown", "clientId": "example_client_id"}, "request": {"userAttributes": {"sub": "12345678-1234-1234-1234-123456789012", "email_verified": "true", "email": "<EMAIL>"}, "privateChallengeParameters": {"answer": "123456"}, "challengeAnswer": "123456"}, "response": {}}