package main

import (
    "context"
    "crypto/rand"
    "fmt"
    "log"
    "math/big"
    "os"

    "github.com/aws/aws-lambda-go/lambda"
    "github.com/aws/aws-sdk-go/aws"
    "github.com/aws/aws-sdk-go/aws/session"
    "github.com/aws/aws-sdk-go/service/dynamodb"
    "github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
    "github.com/aws/aws-sdk-go/service/ses"
)

type CreateAuthChallengeRequest struct {
    Request struct {
        UserAttributes map[string]string `json:"userAttributes"`
        Session        []struct {
            ChallengeName    string `json:"challengeName"`
            ChallengeResult  bool   `json:"challengeResult"`
            ChallengeMetadata string `json:"challengeMetadata"`
        } `json:"session"`
    } `json:"request"`
    Response struct {
        PublicChallengeParameters  map[string]string `json:"publicChallengeParameters"`
        PrivateChallengeParameters map[string]string `json:"privateChallengeParameters"`
        ChallengeMetadata          string            `json:"challengeMetadata"`
    } `json:"response"`
}

var (
    sesSender = os.Getenv("SES_SENDER")
)

func handleCreateAuthChallenge(ctx context.Context, event CreateAuthChallengeRequest) (CreateAuthChallengeRequest, error) {
    log.Printf("Received event: %+v", event)

    mfaCode, err := generateMFACode()
    if err != nil {
        return event, fmt.Errorf("failed to generate MFA code: %v", err)
    }

    // Store the MFA code in DynamoDB
    err = storeMFACodeInDynamoDB(event.Request.UserAttributes["email"], mfaCode)
    if err != nil {
        return event, fmt.Errorf("failed to store MFA code: %v", err)
    }

    event.Response.PublicChallengeParameters = map[string]string{
        "email": event.Request.UserAttributes["email"],
    }
    event.Response.PrivateChallengeParameters = map[string]string{}
    event.Response.ChallengeMetadata = "CUSTOM_CHALLENGE"

    err = sendMFACodeToUser(event.Request.UserAttributes["email"], mfaCode)
    if err != nil {
        return event, fmt.Errorf("failed to send MFA code: %v", err)
    }

    log.Printf("Returning response: %+v", event)
    return event, nil
}

func generateMFACode() (string, error) {
    const charset = "0123456789"
    const length = 6
    result := make([]byte, length)
    for i := range result {
        num, err := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
        if err != nil {
            return "", err
        }
        result[i] = charset[num.Int64()]
    }
    return string(result), nil
}

func storeMFACodeInDynamoDB(email, mfaCode string) error {
    sess, err := session.NewSession(&aws.Config{Region: aws.String("us-west-2")})
    if err != nil {
        return err
    }

    svc := dynamodb.New(sess)

    item := map[string]interface{}{
        "email":    email,
        "mfa_code": mfaCode,
    }

    av, err := dynamodbattribute.MarshalMap(item)
    if err != nil {
        return err
    }

    input := &dynamodb.PutItemInput{
        TableName: aws.String("mfa_codes"),
        Item:      av,
    }

    _, err = svc.PutItem(input)
    return err
}

func sendMFACodeToUser(email, mfaCode string) error {
    sess, err := session.NewSession(&aws.Config{Region: aws.String("us-west-2")})
    if err != nil {
        return err
    }

    svc := ses.New(sess)
    input := &ses.SendEmailInput{
        Destination: &ses.Destination{
            ToAddresses: []*string{aws.String(email)},
        },
        Message: &ses.Message{
            Body: &ses.Body{
                Text: &ses.Content{
                    Charset: aws.String("UTF-8"),
                    Data:    aws.String("Your MFA code is: " + mfaCode),
                },
            },
            Subject: &ses.Content{
                Charset: aws.String("UTF-8"),
                Data:    aws.String("Your MFA Code"),
            },
        },
        Source: aws.String(sesSender),
    }

    _, err = svc.SendEmail(input)
    return err
}

func main() {
    lambda.Start(handleCreateAuthChallenge)
}
