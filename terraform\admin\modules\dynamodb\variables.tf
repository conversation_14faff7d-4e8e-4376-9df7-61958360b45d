variable "aws_region" {
  description = "The AWS region"
  type        = string
  
}

variable "table_name" {
  description = "The name of the DynamoDB table"
  type        = string
  
}

variable "dynamo_table_users_name" {
  description = "The name of the DynamoDB table"
  type        = string
  
}

variable "aws_sns_topic_name" {
  description = "The name of the SNS topic"
  type        = string
  
}
variable "hash_key" {
  description = "The hash key of the DynamoDB table"
  type        = string
  
}


//dynamodb ttop table name
variable "dynamo_table_totp_name" {
  description = "The TOTP secrets"
  type        = string
  
}

variable "range_key" {
    description = "The range key of the DynamoDB table"
    type        = string
}

variable "read_capacity" {
    description = "The read capacity of the DynamoDB table"
    type        = number
}

variable "write_capacity" {
    description = "The write capacity of the DynamoDB table"
    type        = number
}

variable "dynamo_table_orders_name" {
  description = "Name of the DynamoDB Orders table"
  type        = string
}

