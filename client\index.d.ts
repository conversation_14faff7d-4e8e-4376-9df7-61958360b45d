declare module "*.png";
declare module "*.jpg";
declare module "*.svg";

declare var REACT_APP_CART_API_ROUTE: string;
declare var REACT_APP_STRIPE_LIVE_SECRET_KEY: string;
declare var REACT_APP_TEST_STRIPE_SECRET_KEY: string;
declare var REACT_APP_PRODUCT_API_ROUTE: string;
declare var REACT_APP_CONTACT_API_ROUTE: string;
declare var REACT_APP_PUBLIC_KEY: string;
declare var REACT_APP_USER_POOL_ID: string;
declare var REACT_APP_CLIENT_ID: string;
declare var REACT_APP_MAINTENANCE_MODE: string;
declare var REACT_APP_S3_BUCKET: string;

declare var REACT_APP_PRESIGNUP_ROUTE: string;

declare var TEST_REACT_APP_CONTACT_API_ROUTE: string;

// // REQUIRED - Amazon Cognito Region
// declare var REACT_APP_AWS_REGION: string;

// // OPTIONAL - Amazon Cognito User Pool ID
// declare var REACT_APP_ADMIN_USER_POOL_ID: string;

// // OPTIONAL - Amazon Cognito Web Client ID (26-char alphanumeric string)
// declare var REACT_APP_CLIENT_ID: string;

// // OPTIONAL - Hosted UI configuration
// declare var REACT_APP_COGNITO_DOMAIN: string;
// declare var   REACT_APP_REDIRECT_SIGN_IN: string;
// declare var REACT_APP_REDIRECT_SIGN_OUT: string;


// src/custom.d.ts

declare module "*.png" {
    const value: string;
    export default value;
  }
  
  declare module "*.jpg" {
    const value: string;
    export default value;
  }
  
  declare module "*.jpeg" {
    const value: string;
    export default value;
  }
  
  declare module "*.gif" {
    const value: string;
    export default value;
  }
  
  declare module "*.webp" {
    const value: string;
    export default value;
  }
  
  declare module "*.svg" {
    import React = require("react");
    export const ReactComponent: React.FC<React.SVGProps<SVGSVGElement>>;
    const src: string;
    export default src;
  }
  
  declare module "*.avif" {
    const value: string;
    export default value;
  }
  