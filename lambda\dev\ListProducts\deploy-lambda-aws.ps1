# PowerShell script to deploy the ListProducts Lambda function using AWS CLI

# Variables
$BUCKET_NAME = "dev-thealpinestudio-lambda-functions-v1"
$FILE_NAME = "list-products.zip"
$LAMBDA_FUNCTION_NAME = "dev-list-products"
$REGION = "us-west-2"
$PROFILE = "thealpinestudio"

Write-Host "=== Deploying $LAMBDA_FUNCTION_NAME using AWS CLI ==="

# Set environment variables for Go build
$env:GOOS = "linux"
$env:GOARCH = "amd64"
$env:CGO_ENABLED = "0"  # Disable CGO to create a static binary

# Build the Lambda function
Write-Host "Building Lambda function..."
go build -o bootstrap listProducts.go
if ($LASTEXITCODE -ne 0) {
    Write-Host "Build failed."
    exit 1
} else {
    Write-Host "Build succeeded."
}

# Zip the executable
Write-Host "Creating zip file..."
Compress-Archive -Path bootstrap -DestinationPath $FILE_NAME -Force
if ($LASTEXITCODE -ne 0) {
    Write-Host "Zip failed."
    exit 1
} else {
    Write-Host "Zip succeeded."
}

# Upload the zipped file to S3
Write-Host "Uploading to S3..."
aws s3 cp $FILE_NAME "s3://$BUCKET_NAME/$FILE_NAME" --region $REGION --profile $PROFILE
if ($LASTEXITCODE -ne 0) {
    Write-Host "S3 upload failed."
    exit 1
} else {
    Write-Host "S3 upload succeeded."
}

# Update the Lambda function with the code from S3
Write-Host "Updating Lambda function..."
aws lambda update-function-code `
    --function-name $LAMBDA_FUNCTION_NAME `
    --s3-bucket $BUCKET_NAME `
    --s3-key $FILE_NAME `
    --region $REGION `
    --profile $PROFILE
if ($LASTEXITCODE -ne 0) {
    Write-Host "Lambda function update failed."
    exit 1
} else {
    Write-Host "Lambda function update succeeded."
}

# Test the Lambda function
Write-Host "Testing Lambda function..."
aws lambda invoke `
    --function-name $LAMBDA_FUNCTION_NAME `
    --payload '{}' `
    --region $REGION `
    --profile $PROFILE `
    response.json

# Display the response
Write-Host "Lambda function response:"
Get-Content response.json

Write-Host "=== Deployment complete ==="
