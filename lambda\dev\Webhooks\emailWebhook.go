package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/resendlabs/resend-go"
	"github.com/stripe/stripe-go/v75"
	"github.com/stripe/stripe-go/v75/webhook"
)

// Constants for SES email sending
const (
	Sender    = "<EMAIL>"
	Recipient = "<EMAIL>"
	Subject   = "Order Confirmation"
	CharSet   = "UTF-8"
	Tolerance = 5 * time.Minute // 5 minutes tolerance for timestamp verification
)

type CheckoutSession struct {
	CustomerDetails struct {
		Email string `json:"email"`
	} `json:"customer_details"`
	AmountTotal     int    `json:"amount_total"`
	Currency        string `json:"currency"`
	ShippingDetails struct {
		Address struct {
			Line1      string `json:"line1"`
			Line2      string `json:"line2"`
			City       string `json:"city"`
			State      string `json:"state"`
			PostalCode string `json:"postal_code"`
			Country    string `json:"country"`
		} `json:"address"`
		Name string `json:"name"`
	} `json:"shipping_details"`
	// Assume a single product; in real scenarios, this could be a slice of products
	Products []struct {
		ID       string `json:"id"`
		Name     string `json:"name"`
		Size     string `json:"size"`
		Currency string `json:"currency"`
		Quantity int64  `json:"quantity"`
		Price    int64  `json:"price"`
	}
}

func handleWebhook(request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	log.Printf("Signature Header: %s", request.Headers["Stripe-Signature"])

	stripe.Key = os.Getenv("STRIPE_SECRET_KEY")
	endpointSecret := os.Getenv("STRIPE_ENDPOINT_SECRET")
	debugMode := os.Getenv("DEBUG_MODE")

	if debugMode == "true" {
		log.Println("DEBUG_MODE is enabled. Skipping signature verification.")
		// Simulate a Stripe event for testing
		var event stripe.Event
		err := json.Unmarshal([]byte(request.Body), &event)
		if err != nil {
			log.Printf("Failed to unmarshal test event: %v\n", err)
			return events.APIGatewayProxyResponse{StatusCode: 400, Body: "Error processing test event."}, nil
		}

		log.Printf("Simulated event type: %s\n", event.Type)

		if event.Type == "checkout.session.completed" {
			log.Println("Handling checkout.session.completed event")

			var checkoutSession CheckoutSession
			err = json.Unmarshal(event.Data.Raw, &checkoutSession)
			if err != nil {
				log.Printf("Failed to unmarshal checkout session: %v\n", err)
				return events.APIGatewayProxyResponse{StatusCode: 400, Body: "Error processing event."}, nil
			}

			log.Printf("CheckoutSession: %+v", checkoutSession)
			sendEmail(checkoutSession)
		}
	} else {
		signatureHeader := request.Headers["Stripe-Signature"]
		if signatureHeader == "" {
			signatureHeader = request.Headers["stripe-signature"] // Handle lowercase header
		}
		if signatureHeader == "" {
			log.Println("Missing Stripe-Signature header")
			return events.APIGatewayProxyResponse{StatusCode: 400, Body: "Missing Stripe-Signature header"}, nil
		}

		var err error
		event, err := webhook.ConstructEvent([]byte(request.Body), signatureHeader, endpointSecret)
		if err != nil {
			log.Printf("Error verifying webhook signature: %v\n", err)
			return events.APIGatewayProxyResponse{StatusCode: 400, Body: "Webhook Error: Invalid signature."}, nil
		}

		log.Printf("Webhook event verified successfully: %s\n", event.Type)

		if event.Type == "checkout.session.completed" {
			log.Println("Handling checkout.session.completed event")

			var checkoutSession CheckoutSession
			err = json.Unmarshal(event.Data.Raw, &checkoutSession)
			if err != nil {
				log.Printf("Failed to unmarshal checkout session: %v\n", err)
				return events.APIGatewayProxyResponse{StatusCode: 400, Body: "Error processing event."}, nil
			}

			log.Printf("CheckoutSession: %+v", checkoutSession)
			sendEmail(checkoutSession)
		}
	}

	return events.APIGatewayProxyResponse{StatusCode: 200, Body: "Received"}, nil
}

func sendEmail(session CheckoutSession) {
	// Convert amount from cents to dollars and format it
	formattedAmount := fmt.Sprintf("$%.2f", float64(session.AmountTotal)/100.0)

	// Build the list of products
	productList := ""
	for _, prod := range session.Products {
		productList += fmt.Sprintf(
			`<li>Product: %s, Quantity: %d, Price: $%.2f %s</li>`,
			prod.Name,
			prod.Quantity,
			float64(prod.Price)/100.0,
			prod.Currency,
		)
	}

	msg := fmt.Sprintf(
		`<div style="font-family: Arial, sans-serif; padding: 20px; max-width: 600px; margin: 0 auto; border: 1px solid #e5e5e5;">
            <h2 style="color: #333333;">Hello,</h2>
            <p style="color: #000;">Thank you for your purchase of:</p>
            <ul style="color: #000; padding-left: 20px;">
                %s
            </ul>
            <p style="color: #000;">Here are the details of your order:</p>
            <ul style="color: #000; padding-left: 20px;">
                <li>Total Amount: <strong>%s %s</strong></li>
                <li>Shipping Address:
                    <address style="font-style: normal;">
                        %s<br>
                        %s<br>
                        %s, %s %s<br>
                        %s
                    </address>
                </li>
            </ul>
            <p style="color: #000;">Best regards,</p>
            <h3 style="color: #333333;">Ariel Rodriguez</h3>
            <p style="color: #000;"><em>The Alpine Studio</em></p>
            <div style="text-align: center;">
                <img src="https://alpinestudio-logo.s3.us-west-2.amazonaws.com/LogoOne.png" alt="The Alpine Studio Logo" style=" width: 200px; height: auto;">
            </div>
        </div>`,
		productList,
		formattedAmount,
		session.Currency,
		session.ShippingDetails.Name,
		session.ShippingDetails.Address.Line1,
		session.ShippingDetails.Address.City,
		session.ShippingDetails.Address.State,
		session.ShippingDetails.Address.PostalCode,
		session.ShippingDetails.Address.Country,
	)

	// Check the RESEND_API_KEY environment variable
	apiKey := os.Getenv("RESEND_API_KEY")
	if apiKey == "" {
		log.Println("ERROR: RESEND_API_KEY environment variable is not set")
		return
	}
	log.Println("Using RESEND_API_KEY successfully.")

	// Set up Resend email
	client := resend.NewClient(apiKey)

	params := &resend.SendEmailRequest{
		From:    Sender,
		To:      []string{session.CustomerDetails.Email},
		Subject: Subject,
		Html:    msg,
	}

	log.Printf("Sending email to %s with subject: %s", session.CustomerDetails.Email, Subject)
	resp, err := client.Emails.Send(params)
	if err != nil {
		log.Printf("Failed to send the email using Resend: %v\n", err)
		return
	}

	log.Printf("Email sent successfully. Response: %+v", resp)
}

func main() {
	lambda.Start(handleWebhook)
}
