variable "aws_region" {
  type        = string
  description = "The AWS region"
  
}

variable "aws_account_id" {
  type        = string
  description = "The AWS account ID"
  
}
variable "auth_cognito_pre_signup_function_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles pre sign up"
  
}


variable "auth_cognito_define_auth_challenge_function_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles define auth challenge"
  
}

variable "auth_cognito_post_authentication_function_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles post authentication"
  
}


variable "auth_cognito_post_confirmation_function_name" {
  type        = string
  description = "The name of the Lambda function that handles post confirmation"
  
}

variable "auth_cognito_post_confirmation_function_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles post confirmation"
  
}

variable "auth_cognito_pre_authentication_function_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles pre authentication"
  
}

variable "auth_cognito_verify_auth_challenge_response_function_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles verify auth challenge response"
  
}

variable "auth_cognito_create_auth_challenge_function_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles create auth challenge"
  
}

variable "auth_cognito_pre_signup_function_name" {
  description = "lambda function name for cognito pre sign up"
  type = string
}
variable "auth_cognito_pre_authentication_function_name" {
  description = "lambda function name for cognito pre authentication"
  type = string
}



variable "auth_cognito_define_auth_challenge_function_name" {
  description = "lambda function name for cognito define auth challenge"
  type = string
}
variable "auth_cognito_post_authentication_function_name" {
  description = "lambda function name for cognito post authentication"
  type = string
}


variable "auth_cognito_verify_auth_challenge_response_function_name" {
  description = "lambda function name for cognito verify auth challenge response"
  type = string
} 
variable "auth_cognito_create_auth_challenge_function_name" {
  description = "lambda function name for cognito create auth challenge"
  type = string

}



variable "sns_topic_name" {
  type        = string
  description = "The name of the SNS topic for Cognito"
  
}

variable "pre_sign_up_lambda_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles pre sign up"
  
}

variable "zone_id" {
  description = "The Zone ID of the Route53 zone"
  type        = string
}

variable "dynamo_table_users_name" {
  description = "The name of the DynamoDB table"
  type        = string
  
}