package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/resendlabs/resend-go"
)

const (
	Sender    = "<EMAIL>"
	Recipient = "<EMAIL>"
)

type EmailBody struct {
	FormType    string  `json:"formType"`
	Name        string  `json:"name"`
	Email       string  `json:"email"`
	Size        *string `json:"size,omitempty"`
	Animal      *string `json:"animal,omitempty"`
	Description *string `json:"description,omitempty"`
	Subject     *string `json:"subject,omitempty"`
	Message     *string `json:"message,omitempty"`
}

func getStringValue(s *string) string {
	if s != nil {
		return *s
	}
	return ""
}

func ContactEmailHandler(ctx context.Context, request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	headers := map[string]string{
		"Content-Type":                     "application/json",
		"Access-Control-Allow-Origin":      "*",
		"Access-Control-Allow-Methods":     "OPTIONS,POST,GET",
		"Access-Control-Allow-Headers":     "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token",
		"Access-Control-Allow-Credentials": "true",
	}

	var body EmailBody
	err := json.Unmarshal([]byte(request.Body), &body)
	if err != nil {
		log.Printf("Error unmarshalling request body: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers:    headers, // Include headers here
			Body:       `{"error":"Invalid request body"}`,
		}, nil
	}

	log.Printf("Parsed request body: %+v", body)

	apiKey := os.Getenv("RESEND_API_KEY")
	if apiKey == "" {
		log.Println("RESEND_API_KEY is not set")
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers:    headers, // Include headers here
			Body:       `{"error":"Internal Server Error"}`,
		}, nil
	}

	client := resend.NewClient(apiKey)

	var htmlBody, subject string

	switch body.FormType {
	case "scratchboardInquiry":
		// Handle Scratchboard Inquiry form
		htmlBody = fmt.Sprintf(
			"<p>Name: %s</p><p>Email: %s</p><p>Size: %s</p><p>Animal: %s</p><p>Description: %s</p>",
			body.Name,
			body.Email,
			getStringValue(body.Size),
			getStringValue(body.Animal),
			getStringValue(body.Description),
		)
		subject = "New Scratchboard Inquiry"
	case "generalInquiry":
		// Handle General Contact form
		htmlBody = fmt.Sprintf(
			"<p>Name: %s</p><p>Email: %s</p><p>Subject: %s</p><p>Message: %s</p>",
			body.Name,
			body.Email,
			getStringValue(body.Subject),
			getStringValue(body.Message),
		)
		subject = "New General Inquiry"
	default:
		log.Printf("Unknown formType: %s", body.FormType)
		return events.APIGatewayProxyResponse{
			StatusCode: 400,
			Headers:    headers, // Include headers here
			Body:       `{"error":"Invalid form type"}`,
		}, nil
	}

	params := &resend.SendEmailRequest{
		From:    Sender,
		To:      []string{Recipient},
		Subject: subject,
		Html:    htmlBody,
	}

	response, err := client.Emails.Send(params)
	if err != nil {
		log.Printf("Error sending email: %v", err)
		return events.APIGatewayProxyResponse{
			StatusCode: 500,
			Headers:    headers, // Include headers here
			Body:       `{"error":"Internal Server Error"}`,
		}, nil
	}

	log.Printf("Email sent successfully: %v", response)

	return events.APIGatewayProxyResponse{
		StatusCode: 200,
		Headers:    headers, // Include headers here
		Body:       `{"message":"Email sent successfully"}`,
	}, nil
}

func main() {
	lambda.Start(ContactEmailHandler)
}
