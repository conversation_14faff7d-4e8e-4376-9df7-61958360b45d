// Updated for testing the development deployment pipeline
package main

import (
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"sort"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"

	// AWS SDK v1
	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
)

// Define structures for Product, Size, and Price
type Product struct {
	ID            string   `json:"id" dynamodbav:"ID"`
	Title         string   `json:"title" dynamodbav:"Title"`
	Description   string   `json:"description" dynamodbav:"Description"`
	Category      string   `json:"category" dynamodbav:"category"`
	Subcategories []string `json:"subcategories" dynamodbav:"subcategories"` // Plural
	Sizes         []Size   `json:"sizes" dynamodbav:"sizes"`
	ImageUrl      string   `json:"imageUrl" dynamodbav:"ImageUrl"`
	Sold          *bool    `json:"sold" dynamodbav:"sold"`
}

type Size struct {
	Size  string `json:"size" dynamodbav:"size"`
	Price Price  `json:"price" dynamodbav:"price"`
}

type Price struct {
	Amount   float64 `json:"amount" dynamodbav:"amount"`
	Currency string  `json:"currency" dynamodbav:"currency"`
}

// Handle GET request for listing products with optional filtering and sorting
// and retrieving a single product by ID and Title
func handleProducts(request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Ensure it's a GET request
	if request.HTTPMethod == "GET" {
		// Initialize AWS session and DynamoDB client
		sess, err := session.NewSession(&aws.Config{
			Region: aws.String("us-west-2"), // Update to your region if necessary
		})
		if err != nil {
			fmt.Printf("Failed to create AWS session: %v\n", err)
			return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to create AWS session"})
		}

		dynamoClient := dynamodb.New(sess)

		// Extract query parameters for filtering and sorting
		category := request.QueryStringParameters["category"]                 // e.g., 'Originals'
		subcategoriesParams := request.QueryStringParameters["subcategories"] // e.g., 'Shirts' or 'Hoodies'
		sortOrder := request.QueryStringParameters["sort"]                    // e.g., 'asc' or 'desc'
		searchKeyword := request.QueryStringParameters["searchKeyword"]       // e.g., 'Original'
		fmt.Printf("Received Search Keyword: %s\n", searchKeyword)
		fmt.Printf("QueryStringParameters: %+v\n", request.QueryStringParameters)
		productID := request.QueryStringParameters["id"]       // e.g., '1730993418'
		productTitle := request.QueryStringParameters["title"] // e.g., 'Product Test'

		fmt.Printf("Received parameters - Category: %s, Subcategory: %s, Sort Order: %s, Search: %s, ID: %s, Title: %s\n", category, subcategoriesParams, sortOrder, searchKeyword, productID, productTitle)

		// Initialize products slice
		products := []Product{}

		// Check if both 'id' and 'title' are provided to perform GetItem
		if productID != "" && productTitle != "" {
			// Perform GetItem operation to retrieve a single product
			key := map[string]*dynamodb.AttributeValue{
				"ID": {
					S: aws.String(productID),
				},
				"Title": {
					S: aws.String(productTitle),
				},
			}

			getItemInput := &dynamodb.GetItemInput{
				TableName: aws.String(os.Getenv("TABLE_NAME")),
				Key:       key,
			}

			// Log the GetItemInput for debugging
			fmt.Printf("Constructed GetItemInput: %+v\n", getItemInput)

			// Perform GetItem operation
			result, err := dynamoClient.GetItem(getItemInput)
			if err != nil {
				fmt.Printf("DynamoDB GetItem Error: %s\n", err.Error())
				return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to retrieve product"})
			}

			// Check if the item exists
			if result.Item == nil {
				return jsonResponse(http.StatusNotFound, map[string]string{"message": "Product not found"})
			}

			// Unmarshal the result into a Product
			var product Product
			err = dynamodbattribute.UnmarshalMap(result.Item, &product)
			if err != nil {
				fmt.Printf("Unmarshal Error: %s\n", err.Error())
				return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to process product"})
			}

			// Log the retrieved product
			fmt.Printf("Retrieved Product: %+v\n", product)

			// Update image URL if necessary
			imageBucketName := os.Getenv("IMAGE_BUCKET_NAME")
			if strings.Contains(product.ImageUrl, "admin-thealpinestudio-lambda-functions") {
				// Update image URL to use the new bucket
				imageKey := strings.Split(product.ImageUrl, "/")
				if len(imageKey) > 0 {
					product.ImageUrl = "https://" + imageBucketName + ".s3.amazonaws.com/" + imageKey[len(imageKey)-1]
				}
			}

			// Return the single product as the response
			return jsonResponse(http.StatusOK, product)
		}

		// Proceed with listing products if 'id' and 'title' are not provided
		if category != "" && strings.ToLower(category) != "all" {
			// Build a query on category only
			queryInput := &dynamodb.QueryInput{
				TableName:              aws.String(os.Getenv("TABLE_NAME")),
				IndexName:              aws.String("CategoryIndex"), // Use your appropriate GSI
				KeyConditionExpression: aws.String("#cat = :cat"),
				ExpressionAttributeNames: map[string]*string{
					"#cat": aws.String("category"),
				},
				ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
					":cat": {S: aws.String(category)},
				},
			}

			// Initialize filter expression
			var filterExpression *string

			// Add FilterExpression for search keyword if provided
			if searchKeyword != "" {
				searchFilter := "contains(Title, :search) OR contains(Description, :search)"
				filterExpression = combineFilterExpressions(filterExpression, searchFilter)
				queryInput.ExpressionAttributeValues[":search"] = &dynamodb.AttributeValue{S: aws.String(searchKeyword)}
			}

			// Add FilterExpression for unsold products
			// soldFilter := "attribute_not_exists(sold) OR sold = :false"
			// filterExpression = combineFilterExpressions(filterExpression, soldFilter)
			// queryInput.ExpressionAttributeValues[":false"] = &dynamodb.AttributeValue{BOOL: aws.Bool(false)}

			// 3. If the client provided subcategory filter(s), add a filter expression using contains().
			//    (If subcategoriesParams is empty or equals "none", then skip this so that products with
			//    an empty/null subcategories field are still returned.)
			if subcategoriesParams != "" && strings.ToLower(subcategoriesParams) != "none" {
				// For multiple values (comma-separated) you can split and combine with OR:
				subcatValues := strings.Split(subcategoriesParams, ",")
				var subcatFilters []string
				for i, subcat := range subcatValues {
					key := fmt.Sprintf(":subcat%d", i)
					subcatFilters = append(subcatFilters, fmt.Sprintf("contains(subcategories, %s)", key))
					queryInput.ExpressionAttributeValues[key] = &dynamodb.AttributeValue{S: aws.String(strings.TrimSpace(subcat))}
				}
				combinedSubcatFilter := "(" + strings.Join(subcatFilters, " OR ") + ")"
				filterExpression = combineFilterExpressions(filterExpression, combinedSubcatFilter)
			}

			// Set the combined filter expression
			if filterExpression != nil {
				queryInput.FilterExpression = filterExpression
			}

			// Log the QueryInput for debugging
			fmt.Printf("Constructed QueryInput: %+v\n", queryInput)

			// Perform the query operation
			queryResult, err := dynamoClient.Query(queryInput)
			if err != nil {
				fmt.Printf("DynamoDB Query Error: %s\n", err.Error())
				return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to fetch products"})
			}

			// Unmarshal the query result into a slice of Products
			err = dynamodbattribute.UnmarshalListOfMaps(queryResult.Items, &products)
			if err != nil {
				fmt.Printf("Unmarshal Error: %s\n", err.Error())
				return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to process products"})
			}
		} else {
			// Use Scan to fetch all unsold products and apply search
			scanInput := &dynamodb.ScanInput{
				TableName: aws.String(os.Getenv("TABLE_NAME")),
				ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
					":false": {BOOL: aws.Bool(false)},
				},
			}

			// Initialize filter expression
			var filterExpression *string

			// Filter for unsold products
			soldFilter := "attribute_not_exists(sold) OR sold = :false"
			filterExpression = combineFilterExpressions(filterExpression, soldFilter)

			// Filter for subcategory if provided
			if subcategoriesParams != "" && strings.ToLower(subcategoriesParams) != "none" {
				subcatValues := strings.Split(subcategoriesParams, ",")
				var subcatFilters []string
				for i, subcat := range subcatValues {
					key := fmt.Sprintf(":subcat%d", i)
					subcatFilters = append(subcatFilters, fmt.Sprintf("contains(subcategories, %s)", key))
					scanInput.ExpressionAttributeValues[key] = &dynamodb.AttributeValue{S: aws.String(strings.TrimSpace(subcat))}
				}
				combinedSubcatFilter := "(" + strings.Join(subcatFilters, " OR ") + ")"
				filterExpression = combineFilterExpressions(filterExpression, combinedSubcatFilter)
			}

			// Filter for category if provided
			if category != "" && strings.ToLower(category) != "all" {
				categoryFilter := "#cat = :cat"
				filterExpression = combineFilterExpressions(filterExpression, categoryFilter)
				if scanInput.ExpressionAttributeNames == nil {
					scanInput.ExpressionAttributeNames = map[string]*string{}
				}
				scanInput.ExpressionAttributeNames["#cat"] = aws.String("category")
				scanInput.ExpressionAttributeValues[":cat"] = &dynamodb.AttributeValue{S: aws.String(category)}
			}

			// Add FilterExpression for search keyword if provided
			if searchKeyword != "" {
				searchFilter := "contains(Title, :search) OR contains(Description, :search)"
				filterExpression = combineFilterExpressions(filterExpression, searchFilter)
				scanInput.ExpressionAttributeValues[":search"] = &dynamodb.AttributeValue{S: aws.String(searchKeyword)}
			}

			// Set the combined filter expression
			if filterExpression != nil {
				scanInput.FilterExpression = filterExpression
			}

			// Log the ScanInput for debugging
			fmt.Printf("Constructed ScanInput: %+v\n", scanInput)

			// Perform the scan operation
			scanResult, err := dynamoClient.Scan(scanInput)
			if err != nil {
				fmt.Printf("DynamoDB Scan Error: %s\n", err.Error())
				return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to fetch products"})
			}

			// Unmarshal the result into a slice of Products
			err = dynamodbattribute.UnmarshalListOfMaps(scanResult.Items, &products)
			if err != nil {
				fmt.Printf("Unmarshal Error: %s\n", err.Error())
				return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to process products"})
			}
		}

		// Update image URLs to use the new S3 bucket if necessary
		imageBucketName := os.Getenv("IMAGE_BUCKET_NAME")
		for i, product := range products {
			if strings.Contains(product.ImageUrl, "admin-thealpinestudio-lambda-functions") {
				// Update image URL to use the new bucket
				imageKey := strings.Split(product.ImageUrl, "/")
				if len(imageKey) > 0 {
					products[i].ImageUrl = "https://" + imageBucketName + ".s3.amazonaws.com/" + imageKey[len(imageKey)-1]
				}
			}
		}

		// Validate sortOrder
		if sortOrder != "asc" && sortOrder != "desc" {
			sortOrder = "asc" // Default sort order
		}

		// Sort the products slice based on 'Category' and then 'Title' within each category
		sort.Slice(products, func(i, j int) bool {
			if strings.EqualFold(products[i].Category, products[j].Category) {
				// Categories are the same, sort by Title
				if sortOrder == "asc" {
					return strings.ToLower(products[i].Title) < strings.ToLower(products[j].Title)
				}
				return strings.ToLower(products[i].Title) > strings.ToLower(products[j].Title)
			}
			// Categories are different, sort by Category
			if sortOrder == "asc" {
				return strings.ToLower(products[i].Category) < strings.ToLower(products[j].Category)
			}
			return strings.ToLower(products[i].Category) > strings.ToLower(products[j].Category)
		})

		// Return the product list as the response
		return jsonResponse(http.StatusOK, products)
	}

	// Return an error for unsupported HTTP methods
	return jsonResponse(http.StatusBadRequest, map[string]string{"message": "Unsupported HTTP method"})
}

// Helper function to combine filter expressions
func combineFilterExpressions(existingFilter *string, newFilter string) *string {
	if existingFilter != nil && *existingFilter != "" {
		combined := fmt.Sprintf("(%s) AND (%s)", *existingFilter, newFilter)
		return &combined
	}
	return &newFilter
}

// jsonResponse creates the HTTP response with CORS headers
func jsonResponse(status int, data interface{}) (events.APIGatewayProxyResponse, error) {
	body, err := json.Marshal(data)
	if err != nil {
		fmt.Printf("JSON Marshal Error: %s\n", err.Error())
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       "Failed to create response",
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
				"Access-Control-Allow-Headers":     "Content-Type,Authorization",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: status,
		Body:       string(body),
		Headers: map[string]string{
			"Access-Control-Allow-Origin":      "*",
			"Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
			"Access-Control-Allow-Headers":     "Content-Type,Authorization",
			"Access-Control-Allow-Credentials": "true",
		},
	}, nil
}

func main() {
	// Start the Lambda function
	lambda.Start(handleProducts)
}
