name: Production Environment Deployment

on:
  push:
    branches:
      - main
      - master
    paths:
      - 'lambda/prod/**'
      - 'terraform/production/**'
      - 'client/**'
      - '.github/workflows/prod-deployment.yml'
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag to apply to the deployment'
        required: false
        type: string

jobs:
  check_branch:
    runs-on: ubuntu-latest
    outputs:
      should_deploy: ${{ steps.check.outputs.should_deploy }}
    steps:
      - name: Check branch
        id: check
        run: |
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          elif [[ "${{ github.ref }}" == "refs/heads/main" || "${{ github.ref }}" == "refs/heads/master" ]]; then
            echo "should_deploy=true" >> $GITHUB_OUTPUT
          else
            echo "should_deploy=false" >> $GITHUB_OUTPUT
          fi

  deploy:
    needs: check_branch
    if: needs.check_branch.outputs.should_deploy == 'true'
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.20'

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2

      - name: Create S3 bucket for Terraform state if it doesn't exist
        run: |
          aws s3api head-bucket --bucket prod-thealpinestudio-backend-tf 2>/dev/null || \
          aws s3 mb s3://prod-thealpinestudio-backend-tf --region us-west-2

      - name: Build Lambda Functions
        run: |
          # Build prodListProducts
          cd lambda/prod/ListProducts
          GOOS=linux GOARCH=amd64 go build -o bootstrap listProducts.go
          zip prod-list-products.zip bootstrap
          aws s3 cp prod-list-products.zip s3://prod-thealpinestudio-lambda-functions-v1/list-products.zip
          cd ../..
          
          # Build prodCheckoutProducts
          cd lambda/prod/CheckoutProducts
          GOOS=linux GOARCH=amd64 go build -o bootstrap checkoutProducts.go
          zip prod-checkout-products.zip bootstrap
          aws s3 cp prod-checkout-products.zip s3://prod-thealpinestudio-lambda-functions-v1/checkout-products.zip
          cd ../..
          
          # Build prodContactEmail
          cd lambda/prod/ContactEmail
          GOOS=linux GOARCH=amd64 go build -o bootstrap contactEmail.go
          zip prod-contact-email.zip bootstrap
          aws s3 cp prod-contact-email.zip s3://prod-thealpinestudio-lambda-functions-v1/contact-email.zip
          cd ../..
          
          # Build prodWebhooks
          cd lambda/prod/Webhooks
          GOOS=linux GOARCH=amd64 go build -o bootstrap webhooks.go
          zip prod-webhooks.zip bootstrap
          aws s3 cp prod-webhooks.zip s3://prod-thealpinestudio-lambda-functions-v1/webhooks.zip
          cd ../..

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v2
        with:
          terraform_version: 1.5.0

      - name: Create terraform.tfvars file
        run: |
          cat > terraform/production/prod-env/terraform.tfvars << EOF
          infra_env = "production"
          access_key = "${{ secrets.AWS_ACCESS_KEY_ID }}"
          secret_key = "${{ secrets.AWS_SECRET_ACCESS_KEY }}"
          account_id = "${{ secrets.AWS_ACCOUNT_ID }}"
          zone_id = "${{ secrets.ROUTE53_ZONE_ID }}"
          domain_name = "thealpinestudio.com"
          hosting_bucket_name = "thealpinestudio.com"
          list_products_function_name = "prod-list-products"
          api_gateway_id = "${{ secrets.PROD_API_GATEWAY_ID }}"
          api_gateway_account_id = "${{ secrets.AWS_ACCOUNT_ID }}"
          api_gateway_region = "us-west-2"
          stripe_secret_key = "${{ secrets.STRIPE_LIVE_SECRET_KEY }}"
          stripe_endpoint_secret = "${{ secrets.STRIPE_LIVE_ENDPOINT_SECRET }}"
          resend_api_key = "${{ secrets.RESEND_API_KEY }}"
          frontend_url = "https://thealpinestudio.com"
          dynamodb_orders_table = "prod-orders"
          EOF

      - name: Terraform Init
        run: |
          cd terraform/production/prod-env
          terraform init

      - name: Terraform Plan
        run: |
          cd terraform/production/prod-env
          terraform plan -var-file=terraform.tfvars -out=tfplan

      - name: Terraform Apply
        run: |
          cd terraform/production/prod-env
          terraform apply -auto-approve tfplan

      - name: Test API Endpoints
        run: |
          echo "Testing prod-list-products endpoint..."
          ENDPOINT=$(terraform -chdir=terraform/production/prod-env output -raw api_endpoint_url)
          curl -s "${ENDPOINT}/prod-list-products" | grep -q "products" && echo "Success!" || echo "Failed!"

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: client/package-lock.json
      
      - name: Build Frontend
        run: |
          cd client
          npm ci
          
          # Set environment variables for production
          export NODE_ENV=production
          export REACT_APP_ENVIRONMENT=production
          export REACT_APP_CART_API_ROUTE="${ENDPOINT}/prod-checkout-products"
          export REACT_APP_PRODUCT_API_ROUTE="${ENDPOINT}/prod-list-products"
          export REACT_APP_CONTACT_API_ROUTE="${ENDPOINT}/prod-contact-email"
          export REACT_APP_WEBHOOKS_API_ROUTE="${ENDPOINT}/prod-webhooks"
          export REACT_APP_STRIPE_PUBLIC_KEY="${{ secrets.STRIPE_LIVE_PUBLIC_KEY }}"
          export REACT_APP_S3_BUCKET="thealpinestudio.com"
          
          # Build for production
          npm run build:prod
      
      - name: Deploy Frontend to S3
        run: |
          aws s3 cp client/build/ s3://thealpinestudio.com/ --recursive --region us-west-2
          
          # Invalidate CloudFront cache
          aws cloudfront create-invalidation --distribution-id ETN10ORXOMMDL --paths "/*" --region us-west-2
      
      - name: Create and push tag
        if: ${{ github.event.inputs.tag != '' || github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master' }}
        run: |
          # Set up Git user
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"
          
          # Determine tag name
          if [[ "${{ github.event.inputs.tag }}" != "" ]]; then
            TAG_NAME="${{ github.event.inputs.tag }}"
          else
            # Generate tag based on branch and date
            BRANCH_NAME=${GITHUB_REF#refs/heads/}
            DATE=$(date +'%Y%m%d-%H%M%S')
            TAG_NAME="${BRANCH_NAME}-${DATE}"
          fi
          
          # Create and push tag
          git tag -a "${TAG_NAME}" -m "Production deployment from GitHub Actions workflow"
          git push origin "${TAG_NAME}"
          
          echo "Created and pushed tag: ${TAG_NAME}"
