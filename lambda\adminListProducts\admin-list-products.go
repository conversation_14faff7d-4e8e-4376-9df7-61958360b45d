package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"sort"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"

	"github.com/aws/aws-sdk-go/aws"
	"github.com/aws/aws-sdk-go/aws/session"
	"github.com/aws/aws-sdk-go/service/dynamodb"
	"github.com/aws/aws-sdk-go/service/dynamodb/dynamodbattribute"
)

// Product represents the structure of a product item in DynamoDB
// Corrected Product struct in the listing API
type Product struct {
	ID            string   `json:"id" dynamodbav:"ID"`
	Title         string   `json:"title" dynamodbav:"Title"`
	Description   string   `json:"description" dynamodbav:"Description"`
	Category      string   `json:"category" dynamodbav:"category"`
	Subcategories []string `json:"subcategories" dynamodbav:"subcategories"` // Plural
	Sizes         []Size   `json:"sizes" dynamodbav:"Sizes"`
	ImageUrl      string   `json:"imageUrl" dynamodbav:"ImageUrl"`
	Sold          *bool    `json:"sold" dynamodbav:"sold"`
}

type Size struct {
	Size  string `json:"size" dynamodbav:"Size"`
	Price Price  `json:"price" dynamodbav:"Price"`
}

type Price struct {
	Amount   float64 `json:"amount" dynamodbav:"Amount"`
	Currency string  `json:"currency" dynamodbav:"Currency"`
}

// handleProducts handles the Lambda function's incoming requests
func handleProducts(request events.APIGatewayProxyRequest) (events.APIGatewayProxyResponse, error) {
	// Only handle GET requests
	if request.HTTPMethod == "GET" {
		log.Println("Initializing AWS session and DynamoDB client...")
		// Initialize AWS session
		sess, err := session.NewSession(&aws.Config{
			Region: aws.String("us-west-2"), // Update as needed
		})
		if err != nil {
			log.Printf("Failed to create AWS session: %v\n", err)
			return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to create AWS session"})
		}

		// Create DynamoDB client
		dynamoClient := dynamodb.New(sess)

		// Extract query parameters
		category := request.QueryStringParameters["category"]           // e.g., 'Originals'
		sortOrder := request.QueryStringParameters["sortOrder"]         // e.g., 'asc'
		searchKeyword := request.QueryStringParameters["searchKeyword"] // e.g., 'shirt'
		productID := request.QueryStringParameters["id"]                // e.g., '1730993418'
		productTitle := request.QueryStringParameters["title"]          // e.g., 'Product Test'

		log.Printf("Received parameters - Category: %s, Sort Order: %s, Search: %s, ID: %s, Title: %s\n",
			category, sortOrder, searchKeyword, productID, productTitle)

		// Initialize products slice
		products := []Product{}

		// If both 'id' and 'title' are provided, perform GetItem
		if productID != "" && productTitle != "" {
			log.Println("Attempting to retrieve a single product using GetItem...")
			// Define the key
			key := map[string]*dynamodb.AttributeValue{
				"ID": {
					S: aws.String(productID),
				},
				"Title": {
					S: aws.String(productTitle),
				},
			}

			// Create GetItem input
			getItemInput := &dynamodb.GetItemInput{
				TableName: aws.String(os.Getenv("TABLE_NAME")), // Ensure TABLE_NAME is set
				Key:       key,
			}

			log.Printf("Constructed GetItemInput: %+v\n", getItemInput)

			// Perform GetItem operation
			result, err := dynamoClient.GetItem(getItemInput)
			if err != nil {
				log.Printf("DynamoDB GetItem Error: %s\n", err.Error())
				return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to retrieve product"})
			}

			// Check if item exists
			if result.Item == nil {
				log.Println("Product not found in DynamoDB.")
				return jsonResponse(http.StatusNotFound, map[string]string{"message": "Product not found"})
			}

			// Unmarshal the result into a Product struct
			var product Product
			err = dynamodbattribute.UnmarshalMap(result.Item, &product)
			if err != nil {
				log.Printf("Unmarshal Error: %s\n", err.Error())
				return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to process product"})
			}

			log.Printf("Retrieved Product: %+v\n", product)

			// Update image URL if necessary
			imageBucketName := os.Getenv("IMAGE_BUCKET_NAME") // Ensure IMAGE_BUCKET_NAME is set
			if strings.Contains(product.ImageUrl, "admin-thealpinestudio-lambda-functions") {
				imageKey := strings.Split(product.ImageUrl, "/")
				if len(imageKey) > 0 {
					product.ImageUrl = "https://" + imageBucketName + ".s3.amazonaws.com/" + imageKey[len(imageKey)-1]
					log.Printf("Updated Image URL: %s\n", product.ImageUrl)
				}
			}

			// Return the single product
			return jsonResponse(http.StatusOK, product)
		}

		// If 'id' and 'title' are not provided, proceed with listing products
		log.Println("Proceeding to list products based on filters...")
		// Check if category filter is applied
		if category != "" && strings.ToLower(category) != "all" {
			log.Println("Category filter applied. Initiating DynamoDB Query on GSI 'CategoryIndex'...")
			// Define QueryInput
			queryInput := &dynamodb.QueryInput{
				TableName:              aws.String(os.Getenv("TABLE_NAME")),
				IndexName:              aws.String("CategoryIndex"), // Ensure CategoryIndex exists
				KeyConditionExpression: aws.String("#cat = :cat"),
				ExpressionAttributeNames: map[string]*string{
					"#cat": aws.String("category"), // All lowercase
				},
				ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
					":cat":   {S: aws.String(category)},
					":false": {BOOL: aws.Bool(false)},
				},
			}

			// Initialize filter expression
			var filterExpression *string

			// Add FilterExpression for search keyword if provided
			if searchKeyword != "" {
				searchFilter := "contains(lower(#Title), :search) OR contains(lower(#Description), :search)"
				queryInput.ExpressionAttributeValues[":search"] = &dynamodb.AttributeValue{S: aws.String(strings.ToLower(searchKeyword))}
				filterExpression = combineFilterExpressions(filterExpression, searchFilter)
				queryInput.ExpressionAttributeValues[":search"] = &dynamodb.AttributeValue{S: aws.String(searchKeyword)}
				queryInput.ExpressionAttributeNames["#Title"] = aws.String("Title")
				queryInput.ExpressionAttributeNames["#Description"] = aws.String("Description")
				log.Printf("Added Search Keyword Filter: %s\n", searchFilter)
			}

			// Add FilterExpression for unsold products
			soldFilter := "attribute_not_exists(sold) OR sold = :false" // All lowercase
			filterExpression = combineFilterExpressions(filterExpression, soldFilter)
			log.Printf("Added Sold Filter: %s\n", soldFilter)

			// Set the combined filter expression
			if filterExpression != nil {
				queryInput.FilterExpression = filterExpression
				log.Printf("Combined Filter Expression: %s\n", *filterExpression)
			}

			// Log the QueryInput for debugging
			log.Printf("Constructed QueryInput: %+v\n", queryInput)

			// Perform the query operation with pagination
			log.Println("Executing DynamoDB Query...")
			err := dynamoClient.QueryPages(queryInput,
				func(page *dynamodb.QueryOutput, lastPage bool) bool {
					log.Printf("Processing DynamoDB Query Page: %d items\n", len(page.Items))
					var pageProducts []Product
					err := dynamodbattribute.UnmarshalListOfMaps(page.Items, &pageProducts)
					if err != nil {
						log.Printf("Unmarshal Error: %s\n", err.Error())
						return false // Stop pagination on error
					}
					products = append(products, pageProducts...)
					log.Printf("Accumulated Products Count: %d\n", len(products))
					return !lastPage
				})
			if err != nil {
				log.Printf("DynamoDB QueryPages Error: %s\n", err.Error())
				return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to fetch products"})
			}

			log.Printf("Total products retrieved after Query: %d\n", len(products))
		} else {
			log.Println("No category filter applied. Initiating DynamoDB Scan...")
			// Use Scan to fetch all unsold products and apply search
			scanInput := &dynamodb.ScanInput{
				TableName: aws.String(os.Getenv("TABLE_NAME")),
				ExpressionAttributeValues: map[string]*dynamodb.AttributeValue{
					":false": {BOOL: aws.Bool(false)},
				},
			}

			// Initialize filter expression
			var filterExpression *string

			// Filter for unsold products
			soldFilter := "attribute_not_exists(sold) OR sold = :false" // All lowercase
			filterExpression = combineFilterExpressions(filterExpression, soldFilter)
			log.Printf("Added Sold Filter: %s\n", soldFilter)

			// Add FilterExpression for search keyword if provided
			if searchKeyword != "" {
				searchFilter := "contains(#Title, :search) OR contains(#Description, :search)"
				filterExpression = combineFilterExpressions(filterExpression, searchFilter)
				scanInput.ExpressionAttributeValues[":search"] = &dynamodb.AttributeValue{S: aws.String(searchKeyword)}
				scanInput.ExpressionAttributeNames = map[string]*string{
					"#Title":       aws.String("Title"),
					"#Description": aws.String("Description"),
				}
				log.Printf("Added Search Keyword Filter: %s\n", searchFilter)
			}

			// Set the combined filter expression
			if filterExpression != nil {
				scanInput.FilterExpression = filterExpression
				log.Printf("Combined Filter Expression: %s\n", *filterExpression)
			}

			// Log the ScanInput for debugging
			log.Printf("Constructed ScanInput: %+v\n", scanInput)

			// Perform the scan operation with pagination
			log.Println("Executing DynamoDB Scan...")
			err := dynamoClient.ScanPages(scanInput,
				func(page *dynamodb.ScanOutput, lastPage bool) bool {
					log.Printf("Processing DynamoDB Scan Page: %d items\n", len(page.Items))
					var pageProducts []Product
					err := dynamodbattribute.UnmarshalListOfMaps(page.Items, &pageProducts)
					if err != nil {
						log.Printf("Unmarshal Error: %s\n", err.Error())
						return false // Stop pagination on error
					}
					products = append(products, pageProducts...)
					log.Printf("Accumulated Products Count: %d\n", len(products))
					return !lastPage
				})
			if err != nil {
				log.Printf("DynamoDB ScanPages Error: %s\n", err.Error())
				return jsonResponse(http.StatusInternalServerError, map[string]string{"message": "Failed to fetch products"})
			}

			log.Printf("Total products retrieved after Scan: %d\n", len(products))
		}

		// Update image URLs to use the new S3 bucket if necessary
		imageBucketName := os.Getenv("IMAGE_BUCKET_NAME")
		log.Println("Updating image URLs with the new S3 bucket...")
		for i, product := range products {
			if strings.Contains(product.ImageUrl, "admin-thealpinestudio-lambda-functions") {
				// Update image URL to use the new bucket
				imageKey := strings.Split(product.ImageUrl, "/")
				if len(imageKey) > 0 {
					newImageUrl := "https://" + imageBucketName + ".s3.amazonaws.com/" + imageKey[len(imageKey)-1]
					log.Printf("Updating Image URL for Product ID %s: %s -> %s\n", product.ID, product.ImageUrl, newImageUrl)
					products[i].ImageUrl = newImageUrl
				}
			}
		}

		// Validate sortOrder
		if sortOrder != "asc" && sortOrder != "desc" {
			log.Printf("Invalid sortOrder '%s' provided. Defaulting to 'asc'.\n", sortOrder)
			sortOrder = "asc" // Default sort order
		}

		// Sort the products slice based on 'subcategory' and then 'Title' within each subcategory
		log.Printf("Sorting products by Subcategory (%s) and Title...\n", sortOrder)
		// Updated sort function in the listing API
		sort.Slice(products, func(i, j int) bool {
			subcatI := ""
			subcatJ := ""

			if len(products[i].Subcategories) > 0 {
				subcatI = strings.ToLower(products[i].Subcategories[0])
			}
			if len(products[j].Subcategories) > 0 {
				subcatJ = strings.ToLower(products[j].Subcategories[0])
			}

			if subcatI == subcatJ {
				if sortOrder == "asc" {
					return strings.ToLower(products[i].Title) < strings.ToLower(products[j].Title)
				}
				return strings.ToLower(products[i].Title) > strings.ToLower(products[j].Title)
			}

			if sortOrder == "asc" {
				return subcatI < subcatJ
			}
			return subcatI > subcatJ
		})

		// Log sorted products
		log.Printf("Sorted Products: %+v\n", products)

		// Return the product list as the response
		return jsonResponse(http.StatusOK, products)
	}

	// Handle unsupported HTTP methods
	log.Printf("Unsupported HTTP method: %s\n", request.HTTPMethod)
	return jsonResponse(http.StatusBadRequest, map[string]string{"message": "Unsupported HTTP method"})
}

// combineFilterExpressions combines existing and new filter expressions with AND
func combineFilterExpressions(existingFilter *string, newFilter string) *string {
	if existingFilter != nil && *existingFilter != "" {
		combined := fmt.Sprintf("(%s) AND (%s)", *existingFilter, newFilter)
		return &combined
	}
	return &newFilter
}

// jsonResponse creates the HTTP response with CORS headers
func jsonResponse(status int, data interface{}) (events.APIGatewayProxyResponse, error) {
	body, err := json.Marshal(data)
	if err != nil {
		log.Printf("JSON Marshal Error: %s\n", err.Error())
		return events.APIGatewayProxyResponse{
			StatusCode: http.StatusInternalServerError,
			Body:       "Failed to create response",
			Headers: map[string]string{
				"Access-Control-Allow-Origin":      "*",
				"Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
				"Access-Control-Allow-Headers":     "Content-Type,Authorization",
				"Access-Control-Allow-Credentials": "true",
			},
		}, nil
	}

	return events.APIGatewayProxyResponse{
		StatusCode: status,
		Body:       string(body),
		Headers: map[string]string{
			"Access-Control-Allow-Origin":      "*",
			"Access-Control-Allow-Methods":     "OPTIONS,GET,POST,PUT,DELETE",
			"Access-Control-Allow-Headers":     "Content-Type,Authorization",
			"Access-Control-Allow-Credentials": "true",
		},
	}, nil
}

func main() {
	// Start the Lambda function
	lambda.Start(handleProducts)
}
