#!/bin/bash

#remove build file
rm ./ses-notification

#build contact Products
GOOS=linux GOARCH=amd64 go build -o ses-notification sesNotification.go

#test lambda
sam local invoke SESNotificationHandler --event event.json

#zip binary
zip sesNotification.zip ses-notification

#updates lambda function
aws lambda update-function-code \
    --function-name ses-notification \
    --zip-file fileb://sesNotification.zip \
    --region us-west-2
