package main

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
)

type SESNotification struct {
	NotificationType string    `json:"notificationType"`
	<PERSON><PERSON><PERSON>    `json:"bounce,omitempty"`
	Complaint        Complaint `json:"complaint,omitempty"`
}

type Bounce struct {
	BounceType        string      `json:"bounceType"`
	BouncedRecipients []Recipient `json:"bouncedRecipients"`
}

type Complaint struct {
	ComplainedRecipients []Recipient `json:"complainedRecipients"`
}

type Recipient struct {
	EmailAddress string `json:"emailAddress"`
}

func SESNotificationHandler(ctx context.Context, snsEvent events.SNSEvent) error {
	for _, record := range snsEvent.Records {
		snsRecord := record.SNS
		fmt.Printf("[%s %s] Message received from SNS: %s \n", record.EventSource, snsRecord.Timestamp, snsRecord.Message)

		var sesNotification SESNotification
		err := json.Unmarshal([]byte(snsRecord.Message), &sesNotification)
		if err != nil {
			fmt.Println("Error unmarshalling SES notification:", err)
			continue
		}

		switch sesNotification.NotificationType {
		case "Bounce":
			fmt.Printf("Bounce detected. Type: %s\n", sesNotification.Bounce.BounceType)
			for _, recipient := range sesNotification.Bounce.BouncedRecipients {
				fmt.Printf("Bounced email: %s\n", recipient.EmailAddress)
			}

		case "Complaint":
			for _, recipient := range sesNotification.Complaint.ComplainedRecipients {
				fmt.Printf("Complaint received for email: %s\n", recipient.EmailAddress)
			}
		}
	}

	return nil
}

func main() {
	lambda.Start(SESNotificationHandler)
}
