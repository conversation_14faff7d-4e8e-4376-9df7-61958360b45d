name: Development Environment Deployment

on:
  push:
    branches: [dev, development]
  pull_request:
    branches: [dev, development]
    types: [opened, synchronize, reopened]
  workflow_dispatch:
    inputs:
      tag_name:
        description: "Custom tag name for deployment"
        required: false
        type: string
      skip_tests:
        description: "Skip tests (use with caution)"
        required: false
        type: boolean
        default: false

env:
  AWS_REGION: us-east-1
  NODE_VERSION: "18"
  GO_VERSION: "1.21"
  TERRAFORM_VERSION: "1.6.0"
  ENVIRONMENT: development
  API_BASE_URL: https://api-dev.thealpinestudio.com
  FRONTEND_URL: https://test.thealpinestudio.com
  CLOUDFRONT_DISTRIBUTION_ID: E333R3CHKXLYGZ
  TF_STATE_BUCKET: dev-thealpinestudio-backend-tf

jobs:
  # Validation and linting jobs (run in parallel)
  validate-frontend:
    name: Validate Frontend Code
    runs-on: ubuntu-latest
    if: github.event_name != 'workflow_dispatch' || !inputs.skip_tests

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: Run Prettier check
        run: npm run format:check

      - name: Type check
        run: npm run type-check

  validate-backend:
    name: Validate Backend Code
    runs-on: ubuntu-latest
    if: github.event_name != 'workflow_dispatch' || !inputs.skip_tests

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}
          cache: true

      - name: Cache Go modules
        uses: actions/cache@v3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Download dependencies
        run: go mod download
        working-directory: ./lambda

      - name: Run go vet
        run: go vet ./...
        working-directory: ./lambda

      - name: Run go fmt check
        run: |
          if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
            echo "Go code is not formatted properly:"
            gofmt -s -l .
            exit 1
          fi
        working-directory: ./lambda

  validate-terraform:
    name: Validate Terraform Code
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Cache Terraform providers
        uses: actions/cache@v3
        with:
          path: ~/.terraform.d/plugin-cache
          key: ${{ runner.os }}-terraform-${{ hashFiles('**/.terraform.lock.hcl') }}

      - name: Terraform Format Check
        run: terraform fmt -check -recursive
        working-directory: ./terraform

      - name: Terraform Init
        run: terraform init -backend=false
        working-directory: ./terraform/development

      - name: Terraform Validate
        run: terraform validate
        working-directory: ./terraform/development

  # Security scanning (runs in parallel with validation)
  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    if: github.event_name != 'workflow_dispatch' || !inputs.skip_tests

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: "fs"
          scan-ref: "."
          format: "sarif"
          output: "trivy-results.sarif"

      - name: Upload Trivy scan results
        uses: github/codeql-action/upload-sarif@v2
        if: always()
        with:
          sarif_file: "trivy-results.sarif"

  # Test jobs (run in parallel after validation)
  test-frontend:
    name: Test Frontend
    runs-on: ubuntu-latest
    needs: [validate-frontend]
    if: github.event_name != 'workflow_dispatch' || !inputs.skip_tests

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm test -- --coverage --watchAll=false

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        if: always()
        with:
          file: ./coverage/lcov.info
          flags: frontend

  test-backend:
    name: Test Backend
    runs-on: ubuntu-latest
    needs: [validate-backend]
    if: github.event_name != 'workflow_dispatch' || !inputs.skip_tests

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}
          cache: true

      - name: Cache Go modules
        uses: actions/cache@v3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Download dependencies
        run: go mod download
        working-directory: ./lambda

      - name: Run tests
        run: go test -v -race -coverprofile=coverage.out ./...
        working-directory: ./lambda

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        if: always()
        with:
          file: ./lambda/coverage.out
          flags: backend

  # Build jobs (run in parallel after tests pass)
  build-frontend:
    name: Build Frontend
    runs-on: ubuntu-latest
    needs: [test-frontend, validate-terraform, security-scan]
    if: always() && (needs.test-frontend.result == 'success' || needs.test-frontend.result == 'skipped') && needs.validate-terraform.result == 'success' && (needs.security-scan.result == 'success' || needs.security-scan.result == 'skipped')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: Cache node modules
        uses: actions/cache@v3
        with:
          path: ~/.npm
          key: ${{ runner.os }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-

      - name: Install dependencies
        run: npm ci

      - name: Build frontend
        run: npm run build
        env:
          REACT_APP_API_BASE_URL: ${{ env.API_BASE_URL }}
          REACT_APP_ENVIRONMENT: ${{ env.ENVIRONMENT }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: frontend-build
          path: build/
          retention-days: 7

  build-backend:
    name: Build Backend
    runs-on: ubuntu-latest
    needs: [test-backend, validate-terraform, security-scan]
    if: always() && (needs.test-backend.result == 'success' || needs.test-backend.result == 'skipped') && needs.validate-terraform.result == 'success' && (needs.security-scan.result == 'success' || needs.security-scan.result == 'skipped')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: ${{ env.GO_VERSION }}
          cache: true

      - name: Cache Go modules
        uses: actions/cache@v3
        with:
          path: ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
          restore-keys: |
            ${{ runner.os }}-go-

      - name: Download dependencies
        run: go mod download
        working-directory: ./lambda

      - name: Build Lambda functions
        run: |
          mkdir -p dist
          for dir in */; do
            if [ -f "$dir/main.go" ]; then
              echo "Building $dir"
              cd "$dir"
              GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o "../dist/${dir%/}" main.go
              cd ..
            fi
          done
        working-directory: ./lambda

      - name: Create deployment packages
        run: |
          cd lambda/dist
          for binary in *; do
            if [ -f "$binary" ]; then
              zip "${binary}.zip" "$binary"
              echo "Created ${binary}.zip"
            fi
          done

      - name: Upload Lambda artifacts
        uses: actions/upload-artifact@v3
        with:
          name: lambda-functions
          path: lambda/dist/*.zip
          retention-days: 7

  # Deployment jobs (run sequentially after builds)
  deploy-infrastructure:
    name: Deploy Infrastructure
    runs-on: ubuntu-latest
    needs: [build-frontend, build-backend]
    if: github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/development' || github.event_name == 'workflow_dispatch'
    environment: development

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TERRAFORM_VERSION }}

      - name: Cache Terraform providers
        uses: actions/cache@v3
        with:
          path: ~/.terraform.d/plugin-cache
          key: ${{ runner.os }}-terraform-${{ hashFiles('**/.terraform.lock.hcl') }}

      - name: Terraform Init
        run: |
          terraform init \
            -backend-config="bucket=${{ env.TF_STATE_BUCKET }}" \
            -backend-config="key=development/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_REGION }}"
        working-directory: ./terraform/development

      - name: Terraform Plan
        run: terraform plan -detailed-exitcode -out=tfplan
        working-directory: ./terraform/development
        env:
          TF_VAR_aws_profile: thealpinestudio
          TF_VAR_environment: development

      - name: Terraform Apply
        run: terraform apply -auto-approve tfplan
        working-directory: ./terraform/development

      - name: Get Terraform outputs
        id: terraform-outputs
        run: |
          echo "api_gateway_url=$(terraform output -raw api_gateway_url)" >> $GITHUB_OUTPUT
          echo "lambda_bucket=$(terraform output -raw lambda_bucket)" >> $GITHUB_OUTPUT
        working-directory: ./terraform/development

    outputs:
      api_gateway_url: ${{ steps.terraform-outputs.outputs.api_gateway_url }}
      lambda_bucket: ${{ steps.terraform-outputs.outputs.lambda_bucket }}

  deploy-backend:
    name: Deploy Backend Functions
    runs-on: ubuntu-latest
    needs: [deploy-infrastructure]
    if: github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/development' || github.event_name == 'workflow_dispatch'
    environment: development

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Download Lambda artifacts
        uses: actions/download-artifact@v3
        with:
          name: lambda-functions
          path: ./lambda-dist

      - name: Deploy Lambda functions
        run: |
          for zip_file in lambda-dist/*.zip; do
            if [ -f "$zip_file" ]; then
              function_name=$(basename "$zip_file" .zip)
              echo "Deploying $function_name..."

              # Upload to S3 first
              aws s3 cp "$zip_file" "s3://${{ needs.deploy-infrastructure.outputs.lambda_bucket }}/"

              # Update function code
              aws lambda update-function-code \
                --function-name "dev-$function_name" \
                --s3-bucket "${{ needs.deploy-infrastructure.outputs.lambda_bucket }}" \
                --s3-key "$(basename "$zip_file")" \
                --region ${{ env.AWS_REGION }} || echo "Function dev-$function_name may not exist yet"
            fi
          done

      - name: Wait for functions to update
        run: sleep 10

  deploy-frontend:
    name: Deploy Frontend
    runs-on: ubuntu-latest
    needs: [deploy-infrastructure, deploy-backend]
    if: github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/development' || github.event_name == 'workflow_dispatch'
    environment: development

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Download frontend artifacts
        uses: actions/download-artifact@v3
        with:
          name: frontend-build
          path: ./build

      - name: Deploy to S3
        run: |
          # Get the S3 bucket name from CloudFront distribution
          BUCKET_NAME=$(aws cloudfront get-distribution --id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} --query 'Distribution.DistributionConfig.Origins.Items[0].DomainName' --output text | sed 's/.s3.amazonaws.com//')
          echo "Deploying to bucket: $BUCKET_NAME"

          # Sync files to S3
          aws s3 sync ./build "s3://$BUCKET_NAME" --delete --cache-control "public, max-age=31536000" --exclude "*.html"
          aws s3 sync ./build "s3://$BUCKET_NAME" --delete --cache-control "public, max-age=0, must-revalidate" --include "*.html"

      - name: Invalidate CloudFront
        run: |
          aws cloudfront create-invalidation \
            --distribution-id ${{ env.CLOUDFRONT_DISTRIBUTION_ID }} \
            --paths "/*"

  # Post-deployment verification
  verify-deployment:
    name: Verify Deployment
    runs-on: ubuntu-latest
    needs: [deploy-frontend, deploy-backend]
    if: github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/development' || github.event_name == 'workflow_dispatch'

    steps:
      - name: Wait for deployment to stabilize
        run: sleep 60

      - name: Health check - Frontend
        run: |
          echo "Checking frontend health..."
          response=$(curl -s -o /dev/null -w "%{http_code}" ${{ env.FRONTEND_URL }})
          if [ $response -eq 200 ]; then
            echo "✅ Frontend is healthy (HTTP $response)"
          else
            echo "❌ Frontend health check failed (HTTP $response)"
            exit 1
          fi

      - name: Health check - API
        run: |
          echo "Checking API health..."
          # Try to hit a health endpoint or basic API endpoint
          response=$(curl -s -o /dev/null -w "%{http_code}" "${{ env.API_BASE_URL }}/health" || echo "000")
          if [ $response -eq 200 ] || [ $response -eq 404 ]; then
            echo "✅ API is responding (HTTP $response)"
          else
            echo "⚠️  API health check inconclusive (HTTP $response) - this may be normal if no health endpoint exists"
          fi

  # Create deployment tag
  create-tag:
    name: Create Deployment Tag
    runs-on: ubuntu-latest
    needs: [verify-deployment]
    if: github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/development' || github.event_name == 'workflow_dispatch'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create and push tag
        run: |
          if [ "${{ github.event_name }}" = "workflow_dispatch" ] && [ -n "${{ inputs.tag_name }}" ]; then
            TAG_NAME="${{ inputs.tag_name }}"
          else
            BRANCH_NAME=$(echo ${{ github.ref }} | sed 's/refs\/heads\///')
            TIMESTAMP=$(date +%Y%m%d-%H%M%S)
            TAG_NAME="${BRANCH_NAME}-${TIMESTAMP}"
          fi

          echo "Creating tag: $TAG_NAME"
          git config user.name "github-actions[bot]"
          git config user.email "github-actions[bot]@users.noreply.github.com"
          git tag -a "$TAG_NAME" -m "Deployment to development environment"
          git push origin "$TAG_NAME"

  # Notification job (runs regardless of success/failure)
  notify:
    name: Send Notifications
    runs-on: ubuntu-latest
    needs: [create-tag, verify-deployment]
    if: always() && (github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/development' || github.event_name == 'workflow_dispatch')

    steps:
      - name: Determine deployment status
        id: status
        run: |
          if [ "${{ needs.verify-deployment.result }}" = "success" ] && [ "${{ needs.create-tag.result }}" = "success" ]; then
            echo "status=success" >> $GITHUB_OUTPUT
            echo "message=✅ Development deployment completed successfully!" >> $GITHUB_OUTPUT
          else
            echo "status=failure" >> $GITHUB_OUTPUT
            echo "message=❌ Development deployment failed!" >> $GITHUB_OUTPUT
          fi

      - name: Send Slack notification
        if: env.SLACK_WEBHOOK_URL != ''
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ steps.status.outputs.status }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          message: |
            ${{ steps.status.outputs.message }}

            🌐 Environment: Development
            🔗 Frontend: ${{ env.FRONTEND_URL }}
            🔗 API: ${{ env.API_BASE_URL }}
            📝 Commit: ${{ github.sha }}
            👤 Author: ${{ github.actor }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
