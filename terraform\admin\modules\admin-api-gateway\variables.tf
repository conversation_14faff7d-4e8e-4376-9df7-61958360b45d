//admin/modules/admin-api-gateway/variables.tf
variable "aws_region" {
  description = "The AWS region"
  type        = string
}

variable "api_gateway_account_id" {
  description = "The ID of the AWS account that owns the API Gateway"
  type        = string
}

variable "lambda_execution_role_token_authorizer" {
  description = "The ARN of the Lambda execution role"
  type        = string
  
}

variable "admin_sub_domain" {
  description = "The subdomain for the API Gateway"
  type        = string
}

variable "presign_lambda_function_name" {
  type        = string
  description = "Name of the presigned URL Lambda function"
}

variable "admin_api_gateway" {
  description = "The name of the API Gateway"
  type        = string
}

variable "cognito_admin_user_pool_arn" {
  description = "The ARN of the Cognito User Pool"
  type        = string
  
}

variable "presign_lambda_function_arn" {
  type        = string
  description = "The ARN of the presign Lambda function"
}

variable "admin_user_pool_id" {
  description = "The ID of the Cognito User Pool"
  type        = string

}

variable "root_domain" {
  description = "The root domain for the API Gateway"
  type        = string
  
}

variable "get_lambda_arn" {
  description = "ARN of the GET Lambda function"
  type        = string
  
}
variable "get_lambda_function_name" {
  type        = string
  description = "The name of the GET Lambda function"
}

variable "authorizer_lambda_function_name" {
  description = "The ARN of the Lambda function that handles authorization"
  type        = string
}

variable "authorizer_lambda_function_arn" {
  type        = string
  description = "The name of the Lambda function that handles authorization"
}

variable "put_lambda_arn" {
  description = "ARN of the PUT Lambda function"
  type        = string
  
}

variable "api_gateway_lambda_invoke_role_token_authorizer_arn" {
  description = "The ARN of the IAM role that allows API Gateway to invoke Lambda functions"
  type        = string
  
}

variable "get_product_by_id_lambda_arn" {
  description = "ARN of the Lambda function that retrieves a product by ID"
  type        = string
  
}

variable "get_product_by_id_lambda_function_name" {
  description = "The name of the Lambda function that retrieves a product by ID"
  type        = string
  
}
variable "put_lambda_function_name" {
  type        = string
  description = "The name of the PUT Lambda function"
}

variable "post_lambda_arn" {
  description = "ARN of the POST Lambda function"
  type        = string
  
}

variable "post_lambda_function_name" {
  type        = string
  description = "The name of the POST Lambda function"
}

variable "delete_lambda_arn" {
  description = "ARN of the DELETE Lambda function"
  type        = string
}

variable "delete_lambda_function_name" {
  type        = string
  description = "The name of the DELETE Lambda function"
}

variable "auth_cognito_refresh_token_function_name" {
  type = string
  description = "lambda function name for cognito refresh token"
}

variable "auth_cognito_refresh_token_arn" {
  type = string
  description = "ARN of the auth_refresh_token Lambda function"
}

variable "auth_cognito_login_function_name" {
  type = string
  description = "lambda function name for cognito login"
}

variable "auth_cognito_login_arn" {
  type = string
  description = "ARN of the auth_login Lambda function"
}

variable "auth_cognito_logout_function_name" {
  type = string
  description = "lambda function name for cognito logout"
}

variable "auth_cognito_logout_arn" {
  type = string
  description = "ARN of the auth_logout Lambda function"
}

variable "auth_cognito_forgot_password_function_name" {
  type = string
  description = "lambda function name for cognito forgot password"
}

variable "auth_cognito_forgot_password_arn" {
  type = string
  description = "ARN of the auth_forgot_password Lambda function"
}

variable "auth_cognito_resend_code_function_name" {
  type = string
  description = "lambda function name for cognito resend code"
}

variable "auth_cognito_resend_code_arn" {
  type = string
  description = "ARN of the auth_resend_code Lambda function"
}

//sign up name
variable "auth_cognito_signup_function_name" {
  description = "lambda function name for cognito sign up"
  type = string
}

variable "auth_cognito_signup_function_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles sign up"
  
}

//TTOP name
//ttop name
variable "auth_cognito_top_function_name" {
  description = "lambda function name for cognito logout"
  type = string
}

variable "auth_cognito_ttop_function_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles logout"
  
}

//Confirm sign up name
variable "auth_cognito_confirm_signup_function_name" {
  description = "lambda function name for cognito confirm sign up"
  type = string
}

variable "auth_cognito_confirm_signup_function_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles confirm sign up"
}

//verify mfa name
variable "auth_cognito_verify_mfa_function_name" {
  description = "lambda function name for cognito verify mfa"
  type = string
}

variable "auth_cognito_verify_mfa_lambda_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles verify mfa"
}



//post confirmation name
variable "auth_cognito_post_confirmation_function_name" {
  description = "lambda function name for cognito post confirmation"
  type = string
}

variable "post_confirmation_lambda_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles post confirmation"
}

//sold product name
variable "auth_cognito_sold_product_function_name" {
  description = "lambda function name for cognito sold product"
  type = string
}

variable "auth_cognito_sold_product_lambda_arn" {
  type        = string
  description = "The ARN of the Lambda function that handles sold product"
}