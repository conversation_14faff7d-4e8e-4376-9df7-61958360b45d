#!/bin/bash

# Shell script to deploy all development Lambda functions
# ===================================================

# Configuration
REGION="us-west-2"
PROFILE="thealpinestudio"
BUCKET_NAME="dev-thealpinestudio-lambda-functions-v1"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function definitions array
declare -a FUNCTIONS=(
    "dev-list-products|lambda/dev/ListProducts|listProducts.go|list-products.zip|list-products.zip|{\"httpMethod\":\"GET\",\"queryStringParameters\":{}}"
    "dev-checkout-products|lambda/dev/CheckoutProducts|checkoutProducts.go|checkout-products.zip|checkout-products.zip|{\"httpMethod\":\"POST\",\"body\":\"{\\\"items\\\":[{\\\"id\\\":\\\"1743209634\\\",\\\"title\\\":\\\"All Eyes on You Sticker\\\",\\\"quantity\\\":1,\\\"size\\\":\\\"Sticker\\\",\\\"price\\\":6}],\\\"customerInfo\\\":{\\\"email\\\":\\\"<EMAIL>\\\"}}\"}"
    "dev-contact-email|lambda/dev/ContactEmail|contactEmail.go|contact-email.zip|contact-email.zip|{\"httpMethod\":\"POST\",\"body\":\"{\\\"name\\\":\\\"Test User\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"message\\\":\\\"This is a test message\\\"}\"}"
    "dev-webhooks|lambda/dev/Webhooks|emailWebhook.go|webhooks.zip|webhooks.zip|{\"httpMethod\":\"POST\",\"body\":\"{\\\"type\\\":\\\"checkout.session.completed\\\",\\\"data\\\":{\\\"object\\\":{\\\"id\\\":\\\"cs_test_123\\\"}}}\"}"
)

# Function to deploy a Lambda function
deploy_lambda() {
    local function_name=$1
    local directory=$2
    local source_file=$3
    local zip_file=$4
    local s3_key=$5
    local test_event=$6

    echo -e "${CYAN}=== Deploying $function_name ===${NC}"

    # Navigate to the function directory
    pushd "$directory" > /dev/null

    # Set environment variables for Go build
    export GOOS=linux
    export GOARCH=amd64
    export CGO_ENABLED=0

    # Build the Lambda function
    echo -e "${YELLOW}Building Lambda function...${NC}"
    go build -o bootstrap "$source_file"
    if [ $? -ne 0 ]; then
        echo -e "${RED}Build failed.${NC}"
        popd > /dev/null
        return 1
    else
        echo -e "${GREEN}Build succeeded.${NC}"
    fi

    # Zip the executable
    echo -e "${YELLOW}Creating zip file...${NC}"
    zip "$zip_file" bootstrap
    if [ $? -ne 0 ]; then
        echo -e "${RED}Zip failed.${NC}"
        popd > /dev/null
        return 1
    else
        echo -e "${GREEN}Zip succeeded.${NC}"
    fi

    # Upload the zipped file to S3
    echo -e "${YELLOW}Uploading to S3...${NC}"
    aws s3 cp "$zip_file" "s3://$BUCKET_NAME/$s3_key" --region "$REGION" --profile "$PROFILE"
    if [ $? -ne 0 ]; then
        echo -e "${RED}S3 upload failed.${NC}"
        popd > /dev/null
        return 1
    else
        echo -e "${GREEN}S3 upload succeeded.${NC}"
    fi

    # Update the Lambda function with the code from S3
    echo -e "${YELLOW}Updating Lambda function...${NC}"
    aws lambda update-function-code \
        --function-name "$function_name" \
        --s3-bucket "$BUCKET_NAME" \
        --s3-key "$s3_key" \
        --region "$REGION" \
        --profile "$PROFILE"
    if [ $? -ne 0 ]; then
        echo -e "${RED}Lambda function update failed.${NC}"
        popd > /dev/null
        return 1
    else
        echo -e "${GREEN}Lambda function update succeeded.${NC}"
    fi

    # Return to the original directory
    popd > /dev/null
    return 0
}

# Function to test a Lambda function
test_lambda() {
    local function_name=$1
    local test_event=$2

    echo -e "${CYAN}=== Testing $function_name ===${NC}"

    # Create a temporary test event file
    local test_event_file="test-event-$function_name.json"
    echo "$test_event" > "$test_event_file"

    # Invoke the Lambda function
    echo -e "${YELLOW}Invoking Lambda function...${NC}"
    local response_file="response-$function_name.json"
    aws lambda invoke \
        --function-name "$function_name" \
        --cli-binary-format raw-in-base64-out \
        --payload "file://$test_event_file" \
        --region "$REGION" \
        --profile "$PROFILE" \
        "$response_file"

    # Check if the invocation was successful
    if [ $? -ne 0 ]; then
        echo -e "${RED}Lambda function invocation failed.${NC}"
        return 1
    else
        echo -e "${GREEN}Lambda function invocation succeeded.${NC}"

        # Display the response
        echo -e "${YELLOW}Response:${NC}"
        cat "$response_file"
        echo ""

        # Check if the response contains an error
        if grep -q "FunctionError" "$response_file"; then
            echo -e "${RED}Lambda function returned an error.${NC}"
            return 1
        fi

        return 0
    fi
}

# Main script execution
echo -e "${GREEN}=== Starting Deployment of All Development Lambda Functions ===${NC}"

# Create results arrays
declare -a deploy_results=()
declare -a test_results=()
declare -a function_names=()

# Deploy and test each function
for function_def in "${FUNCTIONS[@]}"; do
    # Split the function info
    IFS='|' read -r function_name directory source_file zip_file s3_key test_event <<< "$function_def"
    
    function_names+=("$function_name")
    
    # Deploy the function
    if deploy_lambda "$function_name" "$directory" "$source_file" "$zip_file" "$s3_key" "$test_event"; then
        deploy_results+=("SUCCESS")
        
        # Wait a moment for the Lambda function to be ready
        sleep 5
        
        # Test the function
        if test_lambda "$function_name" "$test_event"; then
            test_results+=("SUCCESS")
        else
            test_results+=("FAILED")
        fi
    else
        deploy_results+=("FAILED")
        test_results+=("SKIPPED")
    fi
done

# Display summary
echo -e "\n${GREEN}=== Deployment Summary ===${NC}"
printf "%-25s %-15s %-15s\n" "Function" "Deployment" "Test"
printf "%-25s %-15s %-15s\n" "--------" "----------" "----"

for i in "${!function_names[@]}"; do
    printf "%-25s %-15s %-15s\n" "${function_names[$i]}" "${deploy_results[$i]}" "${test_results[$i]}"
done

# Count successes and failures
deploy_successes=$(printf '%s\n' "${deploy_results[@]}" | grep -c "SUCCESS")
deploy_failures=$(printf '%s\n' "${deploy_results[@]}" | grep -c "FAILED")
test_successes=$(printf '%s\n' "${test_results[@]}" | grep -c "SUCCESS")
test_failures=$(printf '%s\n' "${test_results[@]}" | grep -c "FAILED")

echo ""
if [ "$deploy_failures" -eq 0 ]; then
    echo -e "${GREEN}Deployment Results: $deploy_successes succeeded, $deploy_failures failed${NC}"
else
    echo -e "${RED}Deployment Results: $deploy_successes succeeded, $deploy_failures failed${NC}"
fi

if [ "$test_failures" -eq 0 ]; then
    echo -e "${GREEN}Test Results: $test_successes succeeded, $test_failures failed${NC}"
else
    echo -e "${RED}Test Results: $test_successes succeeded, $test_failures failed${NC}"
fi

echo -e "\n${GREEN}=== Deployment Complete ===${NC}"

# Clean up temporary files
rm -f test-event-*.json response-*.json
