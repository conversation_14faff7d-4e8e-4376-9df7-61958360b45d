########################################
# IMAGE RESIZER LAMBDA FUNCTION SETUP
########################################

# Archive and upload the image resizer Lambda code
data "archive_file" "lambda_code_resizer" {
  type        = "zip"
  source_dir  = "../../../lambda/adminImageResizer/" # Path to your resizer lambda code
  output_path = "./image-resizer.zip"
}

resource "aws_s3_object" "lambda_code_resizer" {
  bucket       = var.s3_bucket
  key          = "image-resizer.zip"
  source       = data.archive_file.lambda_code_resizer.output_path
  etag         = filemd5(data.archive_file.lambda_code_resizer.output_path)
  acl          = "private"
  content_type = "application/zip"
}

# Create the resizer Lambda function
resource "aws_lambda_function" "image_resizer_function" {
  function_name    = var.resizer_lambda_function_name
  s3_bucket        = aws_s3_object.lambda_code_resizer.bucket
  s3_key           = aws_s3_object.lambda_code_resizer.key
  role             = aws_iam_role.lambda_execution_role_resizer.arn
  handler          = "image-resizer" # Adjust to your Lambda handler
  runtime       = "provided.al2023"
  source_code_hash = data.archive_file.lambda_code_resizer.output_base64sha256

  environment {
    variables = {
      "TABLE_NAME"        = var.dynamo_table_products_name
      "IMAGE_BUCKET_NAME" = var.image_bucket_name }
  }
}

########################################
# IAM ROLE AND POLICIES FOR RESIZER
########################################

resource "aws_iam_role" "lambda_execution_role_resizer" {
  name = "lambda_execution_role_image_resizer"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect    = "Allow",
        Principal = { Service = "lambda.amazonaws.com" },
        Action    = "sts:AssumeRole"
      }
    ]
  })
}

# Policy to allow the resizer Lambda to read/write S3 and (optionally) update DynamoDB
resource "aws_iam_role_policy" "lambda_execution_policy_resizer" {
  name = "lambda_execution_policy_image_resizer"
  role = aws_iam_role.lambda_execution_role_resizer.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      # Permissions for CloudWatch Logs
      {
        Effect : "Allow",
        Action : [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource : "arn:aws:logs:${var.aws_region}:${data.aws_caller_identity.current.account_id}:*"
      },
      {
        Effect: "Allow",
        Action: [
            "s3:ListBucket",
        ],
        Resource: "arn:aws:s3:::${var.image_bucket_name}"
      },
      # Permissions to read original images and put resized images into the same bucket
      {
        Effect : "Allow",
        Action : [
          "s3:GetObject",
          "s3:PutObject"
        ],
        Resource : "arn:aws:s3:::${var.image_bucket_name}/*"
      },
      # If you need to update DynamoDB with the resized image URL
      {
        Effect : "Allow",
        Action : [
          "dynamodb:UpdateItem",
          "dynamodb:Query"
        ],
        Resource : "arn:aws:dynamodb:${var.aws_region}:${data.aws_caller_identity.current.account_id}:table/${var.dynamo_table_products_name}"
      }
    ]
  })
}

########################################
# PERMISSION FOR S3 TO INVOKE THE RESIZER LAMBDA
########################################

resource "aws_lambda_permission" "allow_s3_invoke_resizer" {
  statement_id  = "AllowS3InvokeResizer"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.image_resizer_function.function_name
  principal     = "s3.amazonaws.com"
  source_arn    = "arn:aws:s3:::${var.image_bucket_name}"
}

########################################
# S3 BUCKET NOTIFICATION FOR RESIZER
########################################

resource "aws_s3_bucket_notification" "resizer_trigger" {
  bucket = var.image_bucket_name

  lambda_function {
    lambda_function_arn = aws_lambda_function.image_resizer_function.arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "originals/" # Trigger only for objects uploaded under "originals/"
  }

  depends_on = [
    aws_lambda_permission.allow_s3_invoke_resizer
  ]
}
