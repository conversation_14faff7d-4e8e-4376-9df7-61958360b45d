# data "archive_file" "lambda_code" {
#   type        = "zip"
#   source_dir  = "../../lambda/"
#   output_path = "./list-products.zip"

#   # depends_on  = [null_resource.install_dependencies]
#   # Exclude the venv/bin/python file
#   # excludes = ["lambda_venv/**"]
# }

resource "aws_s3_bucket" "s3_bucket" {
  bucket = var.s3_bucket
}

resource "aws_s3_bucket_cors_configuration" "s3_list_cors" {
  bucket = aws_s3_bucket.s3_bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_origins = ["https://test.thealpinestudio.com", "https://thealpinestudio.com"]
    allowed_methods = ["GET", "POST", "PUT"]
    max_age_seconds = 3000
  }
}

resource "aws_s3_bucket_cors_configuration" "s3_lambda_cors" {
  bucket = aws_s3_bucket.s3_bucket.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_origins = ["https://test.thealpinestudio.com", "https://thealpinestudio.com"]
    allowed_methods = ["GET", "POST", "PUT"]
    max_age_seconds = 3000
  }
}


resource "aws_iam_role" "lambda_execution_role" {
  name = "lambda_execution_role_${var.list_products_function_name}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Sid    = ""
      Principal = {
        Service = "lambda.amazonaws.com"
      }
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_execution_policy" {
  name = "lambda_execution_policy_${var.list_products_function_name}"
  role = aws_iam_role.lambda_execution_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },
      {
        Effect = "Allow"
        Action = [
          "s3:PutObject",
          "s3:GetObjectAcl",
          "s3:GetObject",
          "s3:DeleteObject"
        ]
        Resource = [
          "arn:aws:s3:::${var.s3_bucket}/*",
          "arn:aws:s3:::${var.s3_bucket}/*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "execute-api:Invoke"
        ]
        Resource = "arn:aws:execute-api:*:*:*"
      },
       {
        Effect = "Allow"
        Action = [
          "dynamodb:PutItem",
          "dynamodb:UpdateItem",
          "dynamodb:GetItem",
          "dynamodb:DescribeTable",
          "dynamodb:DeleteItem",
          "dynamodb:Query",
          "dynamodb:Scan"
        ]
        Resource = "arn:aws:dynamodb:us-west-2:${var.api_gateway_account_id}:table/${var.dynamodb_orders_table}"
      },
      {
        Effect = "Allow"
        Action = [
          "lambda:InvokeFunction"
        ]
        Resource = [
          "arn:aws:lambda:us-west-2:${var.api_gateway_account_id}:function:${var.list_products_function_name}",
          "arn:aws:lambda:us-west-2:${var.api_gateway_account_id}:function:${var.checkout_products_function}",
          "arn:aws:lambda:us-west-2:${var.api_gateway_account_id}:function:${var.webhooks_function}",
          "arn:aws:lambda:us-west-2:${var.api_gateway_account_id}:function:${var.contact_email_function}"
        ]
      }
    ]
  })
}


# resource "aws_iam_role_policy_attachment" "lambda_policy" {
#   role       = aws_iam_role.lambda_execution_role.name
#   policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
# }

# resource "aws_iam_role_policy_attachment" "lambda_policy" {
#   role       = aws_iam_role.lambda_execution_role.name
#   policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
# }






