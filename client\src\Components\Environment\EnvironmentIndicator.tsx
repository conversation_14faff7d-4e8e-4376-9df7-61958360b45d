import React from "react";

interface EnvironmentIndicatorProps {
  showInProduction?: boolean;
}

// Access environment variables through the window object
declare global {
  interface Window {
    REACT_APP_ENVIRONMENT?: string;
  }
}

const EnvironmentIndicator: React.FC<EnvironmentIndicatorProps> = ({
  showInProduction = false,
}) => {
  const environment = window.REACT_APP_ENVIRONMENT || "development";
  const isProduction = environment === "production";

  // Don't show in production unless explicitly requested
  if (isProduction && !showInProduction) {
    return null;
  }

  // For debugging
  console.log("Environment indicator rendering with env:", environment);

  const style: React.CSSProperties = {
    position: "fixed",
    bottom: "10px",
    right: "10px",
    padding: "5px 10px",
    borderRadius: "4px",
    fontSize: "12px",
    fontWeight: "bold",
    zIndex: 9999,
    opacity: 0.8,
    backgroundColor: isProduction ? "#ff4d4f" : "#52c41a",
    color: "white",
  };

  return <div style={style}>{environment.toUpperCase()} ENVIRONMENT</div>;
};

export default EnvironmentIndicator;
