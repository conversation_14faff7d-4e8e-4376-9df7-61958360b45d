//./dev/dev-modules/dev-lambda/list-products.tf
//TEST LIST PRODUCTS FUNCTION

data "archive_file" "lambda_code_list" {
  type        = "zip"
  source_dir  = "../../../lambda/dev/ListProducts"
  output_path = "./dev-list-products.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}
resource "aws_s3_object" "lambda_code" {
  bucket       = var.s3_bucket
  key          = "list-products.zip"
  source       = data.archive_file.lambda_code_list.output_path
  etag         = filemd5(data.archive_file.lambda_code_list.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "list_products_function" {
  function_name = var.list_products_function_name
  s3_bucket     = aws_s3_object.lambda_code.bucket
  s3_key        = aws_s3_object.lambda_code.key
  role          = aws_iam_role.lambda_execution_role_get.arn
  handler       = "dev-list-products"
  runtime       = "provided.al2"
  # filename = "../lambdafunction/package/list-products.zip"
  source_code_hash = data.archive_file.lambda_code_list.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"    = var.s3_bucket
      "s3_bucket_NAME" = var.s3_bucket
      TABLE_NAME       = var.dynamo_table_products_name
    }
  }
}

# Lambda permissions are handled by the API Gateway module to avoid circular dependencies



resource "aws_iam_role" "lambda_execution_role_get" {
  name = "lambda_execution_role_dev_get_products_function"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Action = "sts:AssumeRole"
      Effect = "Allow"
      Sid    = ""
      Principal = {
        Service = "lambda.amazonaws.com"
      }
      }
    ]
  })
}

resource "aws_iam_role_policy" "lambda_execution_policy_get" {
  name = "lambda_execution_policy_dev_list_products_function"
  role = aws_iam_role.lambda_execution_role_get.id

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
        Resource = "arn:aws:logs:*:*:*"
      },

      {
        Effect = "Allow"
        Action = [
          "dynamodb:Query",
          "dynamodb:Scan",
          "dynamodb:GetItem",
          "dynamodb:DescribeTable"
        ]
        Resource = [
          "arn:aws:dynamodb:${var.aws_region}:${var.account_id}:table/${var.dynamo_table_products_name}",
          "arn:aws:s3:::${var.image_bucket_name}",
          "arn:aws:dynamodb:${var.aws_region}:${var.account_id}:table/${var.dynamo_table_products_name}/index/*"

        ]

      },
    ]
  })
}

