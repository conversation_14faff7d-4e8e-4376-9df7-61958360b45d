#!/bin/bash

# Variables
BUCKET_NAME="admin-thealpinestudio-lambda-functions"
FILE_NAME="auth-signup.zip"
LAMBDA_FUNCTION_NAME="auth_signup"
REGION="us-west-2"

# Build the Products Lambda Function
GOOS=linux GOARCH=amd64 go build -o bootstrap signUp.go || { echo 'Build failed'; exit 1; }

# Test the Lambda function locally using SAM CLI
# sam local invoke signUpHandler --event event.json || { echo 'SAM local invoke failed'; exit 1; }

# Zip the bootstrap executable
zip $FILE_NAME bootstrap || { echo 'Zip failed'; exit 1; }

# Upload the zipped file to S3
aws s3 cp $FILE_NAME s3://$BUCKET_NAME/$FILE_NAME --region $REGION || { echo 'S3 upload failed'; exit 1; }

# Update the Lambda function with the code from S3
aws lambda update-function-code \
    --function-name $LAMBDA_FUNCTION_NAME \
    --s3-bucket $BUCKET_NAME \
    --s3-key $FILE_NAME \
    --region $REGION
