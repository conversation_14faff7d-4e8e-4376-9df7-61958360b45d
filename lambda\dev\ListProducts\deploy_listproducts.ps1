# Set variables
$BUCKET_NAME = "dev-thealpinestudio-lambda-functions-v1"
$FILE_NAME = "list-products.zip"
$LAMBDA_FUNCTION_NAME = "dev-list-products"
$REGION = "us-west-2"
$GOOS = "linux"
$GOARCH = "amd64"

# Build the Go Lambda function
Write-Host "Building Go executable..."
$env:GOOS = $GOOS
$env:GOARCH = $GOARCH
go build -o bootstrap listProducts.go

if ($LASTEXITCODE -ne 0) {
    Write-Error "Build failed."
    exit 1
} else {
    Write-Host "Build succeeded."
}

# Zip the bootstrap file
Write-Host "Zipping..."
if (Test-Path $FILE_NAME) { Remove-Item $FILE_NAME }
Compress-Archive -Path bootstrap -DestinationPath $FILE_NAME

if (-not (Test-Path $FILE_NAME)) {
    Write-Error "Zip failed."
    exit 1
} else {
    Write-Host "Zip succeeded."
}

# Upload to S3
Write-Host "Uploading to S3..."
aws s3 cp $FILE_NAME "s3://$BUCKET_NAME/$FILE_NAME" --region $REGION

if ($LASTEXITCODE -ne 0) {
    Write-Error "S3 upload failed."
    exit 1
} else {
    Write-Host "S3 upload succeeded."
}

# Update Lambda function
Write-Host "Updating Lambda function..."
aws lambda update-function-code `
    --function-name $LAMBDA_FUNCTION_NAME `
    --s3-bucket $BUCKET_NAME `
    --s3-key $FILE_NAME `
    --region $REGION

if ($LASTEXITCODE -ne 0) {
    Write-Error "Lambda function update failed."
    exit 1
} else {
    Write-Host "Lambda function update succeeded."
}
