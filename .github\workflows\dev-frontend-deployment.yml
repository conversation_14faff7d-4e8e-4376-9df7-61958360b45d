name: Dev Frontend Deployment

on:
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag to create (optional)'
        required: false
  push:
    branches: [ dev, develop ]
    paths:
      - 'client/**'

jobs:
  deploy-frontend:
    name: Deploy Frontend to Development Environment
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v2
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-west-2
      
      - name: Get API Gateway Endpoint
        id: get-endpoint
        run: |
          # Get the API Gateway endpoint from Terraform state or SSM Parameter Store
          ENDPOINT=$(aws ssm get-parameter --name "/dev/api/endpoint" --query "Parameter.Value" --output text || echo "https://api-dev.thealpinestudio.com")
          echo "ENDPOINT=$ENDPOINT" >> $GITHUB_ENV
          echo "API Endpoint: $ENDPOINT"
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "18"
          cache: "npm"
          cache-dependency-path: client/package-lock.json
      
      - name: Build Frontend
        run: |
          cd client
          npm ci
          
          # Set environment variables for development
          export NODE_ENV=development
          export REACT_APP_ENVIRONMENT=development
          export REACT_APP_CART_API_ROUTE="${ENDPOINT}/dev-checkout-products"
          export REACT_APP_PRODUCT_API_ROUTE="${ENDPOINT}/dev-list-products"
          export REACT_APP_CONTACT_API_ROUTE="${ENDPOINT}/dev-contact-email"
          export REACT_APP_WEBHOOKS_API_ROUTE="${ENDPOINT}/dev-webhooks"
          export REACT_APP_STRIPE_PUBLIC_KEY="${{ secrets.STRIPE_PUBLIC_KEY }}"
          export REACT_APP_S3_BUCKET="dev-thealpinestudio-hosting"
          
          # Build for development
          npm run build:dev
      
      - name: Deploy Frontend to S3
        run: |
          aws s3 cp client/build/ s3://test.thealpinestudio.com-v1/ --recursive --region us-west-2
          
          # Invalidate CloudFront cache
          aws cloudfront create-invalidation --distribution-id E333R3CHKXLYGZ --paths "/*" --region us-west-2
      
      - name: Create and push tag
        if: ${{ github.event.inputs.tag != '' || github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/develop' }}
        run: |
          # Set up Git user
          git config --global user.name "GitHub Actions"
          git config --global user.email "<EMAIL>"
          
          # Determine tag name
          if [[ "${{ github.event.inputs.tag }}" != "" ]]; then
            TAG_NAME="${{ github.event.inputs.tag }}"
          else
            # Generate tag based on branch and date
            BRANCH_NAME=${GITHUB_REF#refs/heads/}
            DATE=$(date +'%Y%m%d-%H%M%S')
            TAG_NAME="frontend-${BRANCH_NAME}-${DATE}"
          fi
          
          # Create and push tag
          git tag -a "${TAG_NAME}" -m "Frontend deployment from GitHub Actions workflow"
          git push origin "${TAG_NAME}"
          
          echo "Created and pushed tag: ${TAG_NAME}"
