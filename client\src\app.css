/* src/App.css */

/* Correct @font-face */
@font-face {
  font-family: "Proxima Nova";
  src: url("https://fonts.cdnfonts.com/s/12345/proxima-nova-condensed.woff2")
      format("woff2"),
    url("https://fonts.cdnfonts.com/s/12345/proxima-nova-condensed.woff")
      format("woff");
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

:root {
  /* If your site’s success color is #8A9A5B */
  --toastify-color-success: #8a9a5b;

  /* If your site’s error color is #CB4154 */
  --toastify-color-error: #cb4154;

  /* If your site’s info color is #6A5ACD */
  --toastify-color-info: #6a5acd;

  /* If your site’s warning color is #FFDB58 */
  --toastify-color-warning: #ffdb58;

  /* The rest you can keep as-is or adjust at will */
  --toastify-color-light: #fff;
  --toastify-color-dark: #121212;
  --toastify-color-transparent: rgba(255, 255, 255, 0.7);

  --toastify-icon-color-info: var(--toastify-color-info);
  --toastify-icon-color-success: var(--toastify-color-success);
  --toastify-icon-color-warning: var(--toastify-color-warning);
  --toastify-icon-color-error: var(--toastify-color-error);

  --toastify-container-width: fit-content;
  --toastify-toast-width: 320px;
  --toastify-toast-offset: 16px;
  --toastify-toast-top: max(
    var(--toastify-toast-offset),
    env(safe-area-inset-top)
  );
  --toastify-toast-right: max(
    var(--toastify-toast-offset),
    env(safe-area-inset-right)
  );
  --toastify-toast-left: max(
    var(--toastify-toast-offset),
    env(safe-area-inset-left)
  );
  --toastify-toast-bottom: max(
    var(--toastify-toast-offset),
    env(safe-area-inset-bottom)
  );
  --toastify-toast-background: #fff;
  --toastify-toast-padding: 14px;
  --toastify-toast-min-height: 64px;
  --toastify-toast-max-height: 800px;
  --toastify-toast-bd-radius: 6px;
  --toastify-toast-shadow: 0px 4px 12px rgba(0, 0, 0, 0.1);
  --toastify-font-family: sans-serif;
  --toastify-z-index: 9999;
  --toastify-text-color-light: #757575;
  --toastify-text-color-dark: #fff;

  /* Colored theme text overrides */
  --toastify-text-color-info: #fff;
  --toastify-text-color-success: #fff;
  --toastify-text-color-warning: #000; /* e.g., black text on a bright yellow warning */
  --toastify-text-color-error: #fff;

  --toastify-spinner-color: #616161;
  --toastify-spinner-color-empty-area: #e0e0e0;
  --toastify-color-progress-light: linear-gradient(
    to right,
    #4cd964,
    #5ac8fa,
    #007aff,
    #34aadc,
    #5856d6,
    #ff2d55
  );
  --toastify-color-progress-dark: #bb86fc;
  --toastify-color-progress-info: var(--toastify-color-info);
  --toastify-color-progress-success: var(--toastify-color-success);
  --toastify-color-progress-warning: var(--toastify-color-warning);
  --toastify-color-progress-error: var(--toastify-color-error);
  --toastify-color-progress-bgo: 0.2;
}
