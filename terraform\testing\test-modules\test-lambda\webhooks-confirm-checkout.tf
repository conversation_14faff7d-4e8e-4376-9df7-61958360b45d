//WEBHOOKS FUNCTION
data "archive_file" "lambda_code_webhooks" {
  type        = "zip"
  source_dir  = "../../../lambda/testWebhooks"
  output_path = "./testing-webhooks.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "lambda_code_webhooks" {
  bucket       = var.s3_bucket
  key          = "webhooks.zip"
  source       = data.archive_file.lambda_code_webhooks.output_path
  etag         = filemd5(data.archive_file.lambda_code_webhooks.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "webhooks_function" {
  function_name = var.webhooks_function
  s3_bucket     = aws_s3_object.lambda_code_webhooks.bucket
  s3_key        = aws_s3_object.lambda_code_webhooks.key
  role          = aws_iam_role.lambda_execution_role.arn
  handler       = "testing-webhooks"
  runtime          = "provided.al2"
  # filename = "../lambdafunction/package/webhooks.zip"
  source_code_hash = data.archive_file.lambda_code_webhooks.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME" = var.s3_bucket
      "s3_bucket_NAME" = var.s3_bucket
      STRIPE_SECRET_KEY = var.stripe_secret_key
      STRIPE_ENDPOINT_SECRET = var.stripe_endpoint_secret
      RESEND_API_KEY = var.resend_api_key
    }
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_webhooks" {
  statement_id  = "AllowExecutionFromAPIGatewayV1"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.webhooks_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "${var.api_gateway_arn}/*/*"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_GET_webhooks" {
  statement_id  = "AllowExecutionFromAPIGatewayGET"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.webhooks_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.api_gateway_region}:${var.api_gateway_account_id}:${var.api_gateway_id}/*/GET/webhooks"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

resource "aws_lambda_permission" "lambda_api_gw_permissions_POST_webhooks" {
  statement_id  = "AllowExecutionFromAPIGatewayPOST"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.webhooks_function.arn

  principal  = "apigateway.amazonaws.com"
  source_arn = "arn:aws:execute-api:${var.api_gateway_region}:${var.api_gateway_account_id}:${var.api_gateway_id}/*/POST/webhooks"


  lifecycle {
    ignore_changes = [
      statement_id,
      source_arn,
      function_name,
    ]
  }
}

  

 

