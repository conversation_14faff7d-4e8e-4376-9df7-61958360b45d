//.modules/admin-lambda/outputs.tf
output "api_gateway_lambda_invoke_role_token_authorizer_arn" {
  value = aws_iam_role.lambda_execution_role_token_authorizer.arn
  description = "ARN of the Lambda execution role for the token authorizor"
  }

output "get_lambda_arn" {
  value = aws_lambda_function.get_products_function.arn
  description = "ARN of the GET Lambda function"
}

output "get_lambda_function_name" {
  value       = aws_lambda_function.get_products_function.function_name
  description = "Function name for GET Lambda function"
}

output "get_product_by_id_lambda_function_name" {
  value       = aws_lambda_function.get_product_by_id_products_function.function_name
  description = "Function name for get_product_by_id Lambda function"
}

output "get_product_by_id_lambda_arn" {
  value = aws_lambda_function.get_product_by_id_products_function.arn
  description = "ARN of the get_product_by_id Lambda function"
  
}

output "presign_lambda_function_name" {
  value = aws_lambda_function.generate_presigned_url.function_name
  description = "Function name for presign Lambda function"
}

output "presign_lambda_function_arn" {
  value = aws_lambda_function.generate_presigned_url.arn
  description = "Function arn for presign Lambda function"
}

output "put_lambda_function_name" {
  value = aws_lambda_function.put_products_function.function_name
  description = "Function name for PUT Lambda function"
}

output "put_lambda_arn" {
  value = aws_lambda_function.put_products_function.arn
  description = "ARN of the PUT Lambda function"
}

output "post_lambda_function_name" {
  value       = aws_lambda_function.post_products_function.function_name
  description = "Function name for POST Lambda function"
}

output "post_lambda_arn" {
  value = aws_lambda_function.post_products_function.arn
  description = "ARN of the POST Lambda function"
}



output "delete_lambda_arn" {
  value = aws_lambda_function.delete_products_function.arn
  description = "ARN of the DELETE Lambda function"
}

output "delete_lambda_function_name" {
  value       = aws_lambda_function.delete_products_function.function_name
  description = "Function name for DELETE Lambda function"
}

//LOGIN
output "auth_login_lambda_function_arn" {
  value       = aws_lambda_function.auth_login_lambda_functions.arn
  description = "ARN of the auth_login Lambda function"
  
}

output "auth_login_lambda_function_name" {
  value       = aws_lambda_function.auth_login_lambda_functions.function_name
  description = "Function name for auth_login Lambda function"
}

//LOGOUT
output "auth_cognito_logout_function_name" {
  value = aws_lambda_function.auth_logout_lambda_functions.function_name
  description = "Function name for auth_logout Lambda function"
}

output "auth_cognito_logout_function_arn" {
  value = aws_lambda_function.auth_logout_lambda_functions.arn
  description = "ARN of the auth_logout Lambda function"
}

//REFRESH TOKEN
output "auth_cognito_refresh_token_function_name" {
  value = aws_lambda_function.auth_refresh_token_lambda_functions.function_name
  description = "Function name for auth_refresh_token Lambda function"
}

output "auth_cognito_refresh_token_arn" {
  value = aws_lambda_function.auth_refresh_token_lambda_functions.arn
  description = "ARN of the auth_refresh_token Lambda function"
}

//FORGOT PASSWORD
output "auth_cognito_forgot_password_function_name" {
  value = aws_lambda_function.auth_forgot_password_lambda_functions.function_name
  description = "Function name for auth_forgot_password Lambda function"
}

output "auth_cognito_forgot_password_function_arn" {
  value = aws_lambda_function.auth_forgot_password_lambda_functions.arn
  description = "ARN of the auth_forgot_password Lambda function"
}

//TTOP 
output "auth_cognito_ttop_function_name" {
  value = aws_lambda_function.auth_top_lambda_functions.function_name
  description = "Function name for auth_ttop Lambda function"
}

output "auth_cognito_ttop_function_arn" {
  value = aws_lambda_function.auth_top_lambda_functions.arn
  description = "ARN of the auth_ttop Lambda function"
}

//RESEND CODE
output "auth_cognito_resend_code_function_name" {
  value = aws_lambda_function.auth_resend_code_lambda_functions.function_name
  description = "Function name for auth_resend_code Lambda function"
}

output "auth_cognito_resend_code_function_arn" {
  value = aws_lambda_function.auth_resend_code_lambda_functions.arn
  description = "ARN of the auth_resend_code Lambda function"
}

//PRE SIGN UP
output "auth_cognito_pre_signup_function_name" {
  value = aws_lambda_function.auth_pre_signup_lambda_functions.function_name
  description = "Function name for auth_pre_signup Lambda function"
}

//VERIFY MFA
output "auth_cognito_verify_mfa_function_name" {
  value = aws_lambda_function.auth_verify_mfa_lambda_functions.function_name
  description = "Function name for auth_verify_mfa Lambda function"
}

output "auth_cognito_verify_mfa_function_arn" {
  value = aws_lambda_function.auth_verify_mfa_lambda_functions.arn
  description = "ARN of the auth_verify_mfa Lambda function"
}


output "auth_cognito_pre_signup_function_arn" {
  value = aws_lambda_function.auth_pre_signup_lambda_functions.arn
  description = "ARN of the auth_pre_signup Lambda function"
}

//SIGN UP
output "auth_cognito_signup_function_name" {
  value = aws_lambda_function.auth_signup_lambda_functions.function_name
  description = "Function name for auth_signup Lambda function"
}

output "auth_cognito_signup_function_arn" {
  value = aws_lambda_function.auth_signup_lambda_functions.arn
  description = "ARN of the auth_signup Lambda function"
}

//CONFIRM SIGN UP
output "auth_cognito_confirm_signup_function_name" {
  value = aws_lambda_function.auth_confirm_signup_lambda_functions.function_name
  description = "Function name for auth_confirm_signup Lambda function"
}

output "auth_cognito_confirm_signup_function_arn" {
  value = aws_lambda_function.auth_confirm_signup_lambda_functions.arn
  description = "ARN of the auth_confirm_signup Lambda function"
}

//DEFINE AUTH CHALLENGE
output "auth_cognito_define_auth_challenge_function_name" {
  value = aws_lambda_function.auth_define_auth_challenge_lambda_functions.function_name
  description = "Function name for auth_define_auth_challenge Lambda function"
}

output "auth_cognito_define_auth_challenge_function_arn" {
  value = aws_lambda_function.auth_define_auth_challenge_lambda_functions.arn
  description = "ARN of the auth_define_auth_challenge Lambda function"
}

//POST AUTHENTICATION
output "auth_cognito_post_authentication_function_name" {
  value = aws_lambda_function.auth_post_auth_challenge_lambda_functions.function_name
  description = "Function name for auth_post_authentication Lambda function"
}

output "auth_cognito_post_authentication_function_arn" {
  value = aws_lambda_function.auth_post_auth_challenge_lambda_functions.arn
  description = "ARN of the auth_post_authentication Lambda function"
}

//POST CONFIRMATION
output "auth_cognito_post_confirmation_function_name" {
  value = aws_lambda_function.auth_post_confirmation_lambda_functions.function_name
  description = "Function name for auth_post_confirmation Lambda function"
}

output "auth_cognito_post_confirmation_function_arn" {
  value = aws_lambda_function.auth_post_confirmation_lambda_functions.arn
  description = "ARN of the auth_post_confirmation Lambda function"
}

//PRE AUTHENTICATION
output "auth_cognito_pre_authentication_function_name" {
  value = aws_lambda_function.auth_pre_authentication_lambda_functions.function_name
  description = "Function name for auth_pre_authentication Lambda function"
}

output "auth_cognito_pre_authentication_function_arn" {
  value = aws_lambda_function.auth_pre_authentication_lambda_functions.arn
  description = "ARN of the auth_pre_authentication Lambda function"
}

//VERIFY AUTH CHALLENGE RESPONSE
output "auth_cognito_verify_auth_challenge_response_function_name" {
  value = aws_lambda_function.auth_verify_auth_challenge_response_lambda_functions.function_name
  description = "Function name for auth_verify_auth_challenge_response Lambda function"
}

output "auth_cognito_verify_auth_challenge_response_function_arn" {
  value = aws_lambda_function.auth_verify_auth_challenge_response_lambda_functions.arn
  description = "ARN of the auth_verify_auth_challenge_response Lambda function"
}

//CREATE AUTH CHALLENGE
output "auth_cognito_create_auth_challenge_function_name" {
  value = aws_lambda_function.auth_create_auth_challenge_lambda_functions.function_name
  description = "Function name for auth_create_auth_challenge Lambda function"
}

output "auth_cognito_create_auth_challenge_function_arn" {
  value = aws_lambda_function.auth_create_auth_challenge_lambda_functions.arn
  description = "ARN of the auth_create_auth_challenge Lambda function"
}

//SOLD PRODUCT
output "auth_cognito_sold_product_function_name" {
  value = aws_lambda_function.sold_product_lambda_functions.function_name
  description = "Function name for auth_sold_product Lambda function"
}

output "auth_cognito_sold_product_lambda_arn" {
  value = aws_lambda_function.sold_product_lambda_functions.arn
  description = "ARN of the auth_sold_product Lambda function"
}

//Token Authorizer
output "authorizer_lambda_function_name" {
  value = aws_lambda_function.token_authorizer_function.function_name
  description = "Function name for auth_token_authorizer Lambda function"
}

output "authorizer_lambda_function_arn" {
  value = aws_lambda_function.token_authorizer_function.arn
  description = "ARN of the auth_token_authorizer Lambda function"
}

output "lambda_execution_role_token_authorizer" {
  value = aws_iam_role.lambda_execution_role_token_authorizer.arn
  description = "ARN of the Lambda execution role for the token authorizer"
  }
