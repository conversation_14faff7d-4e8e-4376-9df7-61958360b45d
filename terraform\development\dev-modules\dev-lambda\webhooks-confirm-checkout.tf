//WEBHOOKS FUNCTION
data "archive_file" "lambda_code_webhooks" {
  type        = "zip"
  source_dir  = "../../../lambda/dev/Webhooks"
  output_path = "./dev-webhooks.zip"

  # depends_on  = [null_resource.install_dependencies]
  # Exclude the venv/bin/python file
  # excludes = ["lambda_venv/**"]
}

resource "aws_s3_object" "lambda_code_webhooks" {
  bucket       = var.s3_bucket
  key          = "webhooks.zip"
  source       = data.archive_file.lambda_code_webhooks.output_path
  etag         = filemd5(data.archive_file.lambda_code_webhooks.output_path)
  acl          = "private"
  content_type = "application/zip"
}

resource "aws_lambda_function" "webhooks_function" {
  function_name = var.webhooks_function
  s3_bucket     = aws_s3_object.lambda_code_webhooks.bucket
  s3_key        = aws_s3_object.lambda_code_webhooks.key
  role          = aws_iam_role.lambda_execution_role.arn
  handler       = "dev-webhooks"
  runtime       = "provided.al2"
  # filename = "../lambdafunction/package/webhooks.zip"
  source_code_hash = data.archive_file.lambda_code_webhooks.output_base64sha256

  environment {
    variables = {
      "BUCKET_NAME"          = var.s3_bucket
      "s3_bucket_NAME"       = var.s3_bucket
      STRIPE_SECRET_KEY      = var.stripe_secret_key
      STRIPE_ENDPOINT_SECRET = var.stripe_endpoint_secret
      RESEND_API_KEY         = var.resend_api_key
    }
  }
}

# Lambda permissions are handled by the API Gateway module to avoid circular dependencies





